{"version": 3, "sources": ["../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/event-handler.js", "../../js/src/base-component.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "names": ["storeData", "id", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "trim", "getSelectorFromElement", "querySelector", "getElementFromSelector", "getTransitionDurationFromElement", "_window$getComputedSt", "window", "getComputedStyle", "transitionDuration", "transitionDelay", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "split", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "obj", "nodeType", "emulateTransitionEnd", "duration", "called", "emulatedDuration", "addEventListener", "listener", "removeEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "toString", "call", "match", "toLowerCase", "RegExp", "test", "Error", "toUpperCase", "isVisible", "style", "parentNode", "elementStyle", "parentNodeStyle", "display", "visibility", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "hasAttribute", "onDOMContentLoaded", "callback", "readyState", "isRTL", "documentElement", "dir", "mapData", "set", "key", "data", "bs<PERSON><PERSON>", "get", "keyProperties", "delete", "Data", "instance", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "getUidEvent", "uid", "getEvent", "<PERSON><PERSON><PERSON><PERSON>", "events", "handler", "delegationSelector", "uidEventList", "i", "len", "length", "event", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "replace", "custom", "has", "add<PERSON><PERSON><PERSON>", "oneOff", "_normalizeParams", "handlers", "previousFn", "fn", "dom<PERSON><PERSON>s", "querySelectorAll", "target", "this", "<PERSON><PERSON><PERSON><PERSON>", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "on", "one", "_normalizeParams2", "inNamespace", "isNamespace", "startsWith", "elementEvent", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "includes", "removeNamespacedHandlers", "slice", "keyHandlers", "trigger", "args", "jQueryEvent", "$", "isNative", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "defineProperty", "preventDefault", "BaseComponent", "_element", "constructor", "DATA_KEY", "dispose", "getInstance", "NAME", "<PERSON><PERSON>", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "closest", "_this", "classList", "remove", "contains", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "jQueryInterface", "each", "handle<PERSON><PERSON><PERSON>", "alertInstance", "JQUERY_NO_CONFLICT", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "<PERSON><PERSON>", "toggle", "setAttribute", "normalizeData", "val", "normalizeDataKey", "chr", "button", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "SelectorEngine", "matches", "find", "_ref", "concat", "Element", "prototype", "findOne", "children", "_ref2", "child", "parents", "ancestor", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "EVENT_KEY", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "PointerType", "TOUCH", "PEN", "Carousel", "_BaseComponent", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "index", "_this2", "activeIndex", "_getItemIndex", "direction", "_extends", "_handleSwipe", "absDeltax", "abs", "_this3", "_keydown", "_addTouchEventListeners", "_this4", "start", "pointerType", "clientX", "touches", "end", "clearTimeout", "itemImg", "e", "add", "move", "tagName", "indexOf", "_getItemByDirection", "activeElement", "isNextDirection", "isPrevDirection", "lastItemIndex", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "from", "_setActiveIndicatorElement", "indicators", "nextIndicator", "elementInterval", "parseInt", "defaultInterval", "directionalClassName", "orderClassName", "_this5", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "carouselInterface", "action", "TypeError", "ride", "dataApiClickHandler", "slideIndex", "carousels", "parent", "Collapse", "_isTransitioning", "_triggerArray", "SELECTOR_DATA_TOGGLE", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "j<PERSON>y", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "REGEXP_KEYDOWN", "ARROW_UP_KEY", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "flip", "boundary", "reference", "popperConfig", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "disabled", "isActive", "clearMenus", "getParentFromElement", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "focus", "destroy", "update", "stopPropagation", "_getPlacement", "parentDropdown", "isEnd", "getPropertyValue", "placement", "modifiers", "name", "options", "altBoundary", "rootBoundary", "enabled", "dropdownInterface", "toggles", "context", "clickEvent", "dropdownMenu", "dataApiKeydownHandler", "items", "backdrop", "Modal", "_dialog", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_scrollbarWidth", "showEvent", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "transition", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "append<PERSON><PERSON><PERSON>", "_enforceFocus", "transitionComplete", "_this6", "_triggerBackdropTransition", "_this7", "_this8", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "_this9", "animate", "createElement", "className", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "_this10", "isModalOverflowing", "scrollHeight", "clientHeight", "overflowY", "modalTransitionDuration", "paddingLeft", "paddingRight", "round", "right", "innerWidth", "_getScrollbarWidth", "_this11", "actualPadding", "calculatedPadding", "<PERSON><PERSON><PERSON><PERSON>", "marginRight", "<PERSON><PERSON><PERSON><PERSON>", "padding", "margin", "scrollDiv", "scrollbarWidth", "width", "clientWidth", "_this12", "uriAttrs", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "DefaultAllowlist", "*", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "elements", "_loop", "el", "el<PERSON>ame", "nodeName", "attributeList", "allowedAttributes", "attr", "allowedAttributeList", "attrName", "nodeValue", "regExp", "attrRegex", "allowedAttribute", "innerHTML", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "html", "fallbackPlacements", "customClass", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "dataKey", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "findShadowRoot", "attachShadow", "getRootNode", "root", "ShadowRoot", "isInTheDom", "ownerDocument", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "_tip$classList", "complete", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "updateAttachment", "flipModifier", "phase", "_handlePopperPlacementChange", "onFirstUpdate", "CLASS_PREFIX", "eventIn", "eventOut", "_fixTitle", "originalTitleType", "dataAttributes", "dataAttr", "tabClass", "map", "token", "tClass", "popperData", "state", "popper", "Popover", "_getContent", "method", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targetSelector", "targetBCR", "height", "item", "sort", "pageYOffset", "max", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "queries", "link", "join", "listGroup", "SELECTOR_NAV_LINKS", "navItem", "node", "spy", "Tab", "listElement", "itemSelector", "hideEvent", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdown", "autohide", "Toast", "_clearTimeout"], "mappings": ";;;;;slCAOA,ICOQA,EACFC,EDWAC,EAAS,SAAAC,GACb,GACEA,GAAUC,KAAKC,MArBH,IAqBSD,KAAKE,gBACnBC,SAASC,eAAeL,IAEjC,OAAOA,GAGHM,EAAc,SAAAC,GAClB,IAAIC,EAAWD,EAAQE,aAAa,kBAEpC,IAAKD,GAAyB,MAAbA,EAAkB,CACjC,IAAME,EAAWH,EAAQE,aAAa,QAEtCD,EAAWE,GAAyB,MAAbA,EAAmBA,EAASC,OAAS,KAG9D,OAAOH,GAGHI,EAAyB,SAAAL,GAC7B,IAAMC,EAAWF,EAAYC,GAE7B,OAAIC,GACKJ,SAASS,cAAcL,GAAYA,EAGrC,MAGHM,EAAyB,SAAAP,GAC7B,IAAMC,EAAWF,EAAYC,GAE7B,OAAOC,EAAWJ,SAASS,cAAcL,GAAY,MAGjDO,EAAmC,SAAAR,GACvC,IAAKA,EACH,OAAO,EAFyC,IAAAS,EAMJC,OAAOC,iBAAiBX,GAAhEY,EAN4CH,EAM5CG,mBAAoBC,EANwBJ,EAMxBI,gBAEpBC,EAA0BC,OAAOC,WAAWJ,GAC5CK,EAAuBF,OAAOC,WAAWH,GAG/C,OAAKC,GAA4BG,GAKjCL,EAAqBA,EAAmBM,MAAM,KAAK,GACnDL,EAAkBA,EAAgBK,MAAM,KAAK,GAxEf,KA0EtBH,OAAOC,WAAWJ,GAAsBG,OAAOC,WAAWH,KAPzD,GAULM,EAAuB,SAAAnB,GAC3BA,EAAQoB,cAAc,IAAIC,MA7EL,mBAgFjBC,EAAY,SAAAC,GAAG,OAAKA,EAAI,IAAMA,GAAKC,UAEnCC,EAAuB,SAACzB,EAAS0B,GACrC,IAAIC,GAAS,EAEPC,EAAmBF,EADD,EAQxB1B,EAAQ6B,iBA5Fa,iBAuFrB,SAASC,IACPH,GAAS,EACT3B,EAAQ+B,oBAzFW,gBAyFyBD,MAI9CE,YAAW,WACJL,GACHR,EAAqBnB,KAEtB4B,IAGCK,EAAkB,SAACC,EAAeC,EAAQC,GAC9CC,OAAOC,KAAKF,GAAaG,SAAQ,SAAAC,GAC/B,IAnGWjB,EAmGLkB,EAAgBL,EAAYI,GAC5BE,EAAQP,EAAOK,GACfG,EAAYD,GAASpB,EAAUoB,GACnC,UArGAnB,OADSA,EAuGFmB,GArGT,GAAUnB,EAGL,GAAGqB,SAASC,KAAKtB,GAAKuB,MAAM,eAAe,GAAGC,cAoGnD,IAAK,IAAIC,OAAOP,GAAeQ,KAAKN,GAClC,MAAM,IAAIO,MACLhB,EAAciB,cAAdjB,aACQM,EADX,oBACuCG,EADpCT,wBAEmBO,EAFtB,UAOFW,EAAY,SAAApD,GAChB,IAAKA,EACH,OAAO,EAGT,GAAIA,EAAQqD,OAASrD,EAAQsD,YAActD,EAAQsD,WAAWD,MAAO,CACnE,IAAME,EAAe5C,iBAAiBX,GAChCwD,EAAkB7C,iBAAiBX,EAAQsD,YAEjD,MAAgC,SAAzBC,EAAaE,SACU,SAA5BD,EAAgBC,SACY,WAA5BF,EAAaG,WAGjB,OAAO,GA0BHC,EAAO,WAAA,OAAM,cAEbC,EAAS,SAAA5D,GAAO,OAAIA,EAAQ6D,cAE5BC,EAAY,WAAM,IACdC,EAAWrD,OAAXqD,OAER,OAAIA,IAAWlE,SAASmE,KAAKC,aAAa,qBACjCF,EAGF,MAGHG,EAAqB,SAAAC,GACG,YAAxBtE,SAASuE,WACXvE,SAASgC,iBAAiB,mBAAoBsC,GAE9CA,KAIEE,EAAyC,QAAjCxE,SAASyE,gBAAgBC,IC/KjCC,GACElF,EAAY,GACdC,EAAK,EACF,CACLkF,IADK,SACDzE,EAAS0E,EAAKC,QACa,IAAlB3E,EAAQ4E,QACjB5E,EAAQ4E,MAAQ,CACdF,IAAAA,EACAnF,GAAAA,GAEFA,KAGFD,EAAUU,EAAQ4E,MAAMrF,IAAMoF,GAEhCE,IAZK,SAYD7E,EAAS0E,GACX,IAAK1E,QAAoC,IAAlBA,EAAQ4E,MAC7B,OAAO,KAGT,IAAME,EAAgB9E,EAAQ4E,MAC9B,OAAIE,EAAcJ,MAAQA,EACjBpF,EAAUwF,EAAcvF,IAG1B,MAETwF,OAxBK,SAwBE/E,EAAS0E,GACd,QAA6B,IAAlB1E,EAAQ4E,MAAnB,CAIA,IAAME,EAAgB9E,EAAQ4E,MAC1BE,EAAcJ,MAAQA,WACjBpF,EAAUwF,EAAcvF,WACxBS,EAAQ4E,WAMjBI,EAAO,SACHC,EAAUP,EAAKC,GACrBH,EAAQC,IAAIQ,EAAUP,EAAKC,IAFzBK,EAAO,SAIHC,EAAUP,GAChB,OAAOF,EAAQK,IAAII,EAAUP,IAL3BM,EAAO,SAOAC,EAAUP,GACnBF,EAAQO,OAAOE,EAAUP,IC/CvBQ,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GAClBC,EAAW,EACTC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAERC,EAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,WASF,SAASC,EAAY5F,EAAS6F,GAC5B,OAAQA,GAAUA,EAAP,KAAeP,KAAiBtF,EAAQsF,UAAYA,IAGjE,SAASQ,EAAS9F,GAChB,IAAM6F,EAAMD,EAAY5F,GAKxB,OAHAA,EAAQsF,SAAWO,EACnBR,EAAcQ,GAAOR,EAAcQ,IAAQ,GAEpCR,EAAcQ,GAsCvB,SAASE,EAAYC,EAAQC,EAASC,QAA2B,IAA3BA,IAAAA,EAAqB,MAGzD,IAFA,IAAMC,EAAe9D,OAAOC,KAAK0D,GAExBI,EAAI,EAAGC,EAAMF,EAAaG,OAAQF,EAAIC,EAAKD,IAAK,CACvD,IAAMG,EAAQP,EAAOG,EAAaC,IAElC,GAAIG,EAAMC,kBAAoBP,GAAWM,EAAML,qBAAuBA,EACpE,OAAOK,EAIX,OAAO,KAGT,SAASE,EAAgBC,EAAmBT,EAASU,GACnD,IAAMC,EAAgC,iBAAZX,EACpBO,EAAkBI,EAAaD,EAAeV,EAGhDY,EAAYH,EAAkBI,QAAQ3B,EAAgB,IACpD4B,EAASxB,EAAasB,GAY5B,OAVIE,IACFF,EAAYE,GAGGrB,EAAasB,IAAIH,KAGhCA,EAAYH,GAGP,CAACE,EAAYJ,EAAiBK,GAGvC,SAASI,EAAWjH,EAAS0G,EAAmBT,EAASU,EAAcO,GACrE,GAAiC,iBAAtBR,GAAmC1G,EAA9C,CAIKiG,IACHA,EAAUU,EACVA,EAAe,MAP4D,IAAAQ,EAU5BV,EAAgBC,EAAmBT,EAASU,GAAtFC,EAVsEO,EAAA,GAU1DX,EAV0DW,EAAA,GAUzCN,EAVyCM,EAAA,GAWvEnB,EAASF,EAAS9F,GAClBoH,EAAWpB,EAAOa,KAAeb,EAAOa,GAAa,IACrDQ,EAAatB,EAAYqB,EAAUZ,EAAiBI,EAAaX,EAAU,MAEjF,GAAIoB,EACFA,EAAWH,OAASG,EAAWH,QAAUA,MAD3C,CAMA,IAAMrB,EAAMD,EAAYY,EAAiBE,EAAkBI,QAAQ5B,EAAgB,KAC7EoC,EAAKV,EAhFb,SAAoC5G,EAASC,EAAUqH,GACrD,OAAO,SAASrB,EAAQM,GAGtB,IAFA,IAAMgB,EAAcvH,EAAQwH,iBAAiBvH,GAElCwH,EAAWlB,EAAXkB,OAAkBA,GAAUA,IAAWC,KAAMD,EAASA,EAAOnE,WACtE,IAAK,IAAI8C,EAAImB,EAAYjB,OAAQF,KAC/B,GAAImB,EAAYnB,KAAOqB,EAOrB,OANAlB,EAAMoB,eAAiBF,EAEnBxB,EAAQiB,QACVU,EAAaC,IAAI7H,EAASuG,EAAMuB,KAAMR,GAGjCA,EAAGS,MAAMN,EAAQ,CAAClB,IAM/B,OAAO,MA8DPyB,CAA2BhI,EAASiG,EAASU,GA7FjD,SAA0B3G,EAASsH,GACjC,OAAO,SAASrB,EAAQM,GAOtB,OANAA,EAAMoB,eAAiB3H,EAEnBiG,EAAQiB,QACVU,EAAaC,IAAI7H,EAASuG,EAAMuB,KAAMR,GAGjCA,EAAGS,MAAM/H,EAAS,CAACuG,KAsF1B0B,CAAiBjI,EAASiG,GAE5BqB,EAAGpB,mBAAqBU,EAAaX,EAAU,KAC/CqB,EAAGd,gBAAkBA,EACrBc,EAAGJ,OAASA,EACZI,EAAGhC,SAAWO,EACduB,EAASvB,GAAOyB,EAEhBtH,EAAQ6B,iBAAiBgF,EAAWS,EAAIV,KAG1C,SAASsB,EAAclI,EAASgG,EAAQa,EAAWZ,EAASC,GAC1D,IAAMoB,EAAKvB,EAAYC,EAAOa,GAAYZ,EAASC,GAE9CoB,IAILtH,EAAQ+B,oBAAoB8E,EAAWS,EAAIa,QAAQjC,WAC5CF,EAAOa,GAAWS,EAAGhC,WAe9B,IAAMsC,EAAe,CACnBQ,GADmB,SAChBpI,EAASuG,EAAON,EAASU,GAC1BM,EAAWjH,EAASuG,EAAON,EAASU,GAAc,IAGpD0B,IALmB,SAKfrI,EAASuG,EAAON,EAASU,GAC3BM,EAAWjH,EAASuG,EAAON,EAASU,GAAc,IAGpDkB,IATmB,SASf7H,EAAS0G,EAAmBT,EAASU,GACvC,GAAiC,iBAAtBD,GAAmC1G,EAA9C,CADqD,IAAAsI,EAKJ7B,EAAgBC,EAAmBT,EAASU,GAAtFC,EAL8C0B,EAAA,GAKlC9B,EALkC8B,EAAA,GAKjBzB,EALiByB,EAAA,GAM/CC,EAAc1B,IAAcH,EAC5BV,EAASF,EAAS9F,GAClBwI,EAAc9B,EAAkB+B,WAAW,KAEjD,QAA+B,IAApBjC,EAAX,CAUIgC,GACFnG,OAAOC,KAAK0D,GAAQzD,SAAQ,SAAAmG,IA1ClC,SAAkC1I,EAASgG,EAAQa,EAAW8B,GAC5D,IAAMC,EAAoB5C,EAAOa,IAAc,GAE/CxE,OAAOC,KAAKsG,GAAmBrG,SAAQ,SAAAsG,GACrC,GAAIA,EAAWC,SAASH,GAAY,CAClC,IAAMpC,EAAQqC,EAAkBC,GAEhCX,EAAclI,EAASgG,EAAQa,EAAWN,EAAMC,gBAAiBD,EAAML,wBAoCrE6C,CAAyB/I,EAASgG,EAAQ0C,EAAchC,EAAkBsC,MAAM,OAIpF,IAAMJ,EAAoB5C,EAAOa,IAAc,GAC/CxE,OAAOC,KAAKsG,GAAmBrG,SAAQ,SAAA0G,GACrC,IAAMJ,EAAaI,EAAYnC,QAAQ1B,EAAe,IAEtD,IAAKmD,GAAe7B,EAAkBoC,SAASD,GAAa,CAC1D,IAAMtC,EAAQqC,EAAkBK,GAEhCf,EAAclI,EAASgG,EAAQa,EAAWN,EAAMC,gBAAiBD,EAAML,4BAvB3E,CAEE,IAAKF,IAAWA,EAAOa,GACrB,OAGFqB,EAAclI,EAASgG,EAAQa,EAAWL,EAAiBI,EAAaX,EAAU,SAsBtFiD,QA/CmB,SA+CXlJ,EAASuG,EAAO4C,GACtB,GAAqB,iBAAV5C,IAAuBvG,EAChC,OAAO,KAGT,IAKIoJ,EALEC,EAAIvF,IACJ+C,EAAYN,EAAMO,QAAQ3B,EAAgB,IAC1CoD,EAAchC,IAAUM,EACxByC,EAAW5D,EAAasB,IAAIH,GAG9B0C,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EACnBC,EAAM,KA4CV,OA1CInB,GAAec,IACjBD,EAAcC,EAAEhI,MAAMkF,EAAO4C,GAE7BE,EAAErJ,GAASkJ,QAAQE,GACnBG,GAAWH,EAAYO,uBACvBH,GAAkBJ,EAAYQ,gCAC9BH,EAAmBL,EAAYS,sBAG7BP,GACFI,EAAM7J,SAASiK,YAAY,eACvBC,UAAUlD,EAAW0C,GAAS,GAElCG,EAAM,IAAIM,YAAYzD,EAAO,CAC3BgD,QAAAA,EACAU,YAAY,SAKI,IAATd,GACT9G,OAAOC,KAAK6G,GAAM5G,SAAQ,SAAAmC,GACxBrC,OAAO6H,eAAeR,EAAKhF,EAAK,CAC9BG,IAD8B,WAE5B,OAAOsE,EAAKzE,SAMhB+E,GACFC,EAAIS,iBAGFX,GACFxJ,EAAQoB,cAAcsI,GAGpBA,EAAID,uBAA2C,IAAhBL,GACjCA,EAAYe,iBAGPT,ICpTLU,EAAAA,WACJ,SAAAA,EAAYpK,GACLA,IAIL0H,KAAK2C,SAAWrK,EAChBgF,EAAahF,EAAS0H,KAAK4C,YAAYC,SAAU7C,0BAGnD8C,QAAA,WACExF,EAAgB0C,KAAK2C,SAAU3C,KAAK4C,YAAYC,UAChD7C,KAAK2C,SAAW,QAKXI,YAAP,SAAmBzK,GACjB,OAAOgF,EAAahF,EAAS0H,KAAK6C,mDAIlC,MAxBY,oBAEVH,GCQAM,EAAO,QAqBPC,EAAAA,SAAAA,uFASJC,MAAA,SAAM5K,GACJ,IAAM6K,EAAc7K,EAAU0H,KAAKoD,gBAAgB9K,GAAW0H,KAAK2C,SAC7DU,EAAcrD,KAAKsD,mBAAmBH,GAExB,OAAhBE,GAAwBA,EAAYtB,kBAIxC/B,KAAKuD,eAAeJ,MAKtBC,gBAAA,SAAgB9K,GACd,OAAOO,EAAuBP,IAAYA,EAAQkL,QAAR,aAG5CF,mBAAA,SAAmBhL,GACjB,OAAO4H,EAAasB,QAAQlJ,EAzCf,qBA4CfiL,eAAA,SAAejL,GAAS,IAAAmL,EAAAzD,KAGtB,GAFA1H,EAAQoL,UAAUC,OAvCC,QAyCdrL,EAAQoL,UAAUE,SA1CJ,QA0CnB,CAKA,IAAM1K,EAAqBJ,EAAiCR,GAE5D4H,EAAaS,IAAIrI,EJ7EE,iBI6EuB,WAAA,OAAMmL,EAAKI,gBAAgBvL,MACrEyB,EAAqBzB,EAASY,QAP5B8G,KAAK6D,gBAAgBvL,MAUzBuL,gBAAA,SAAgBvL,GACVA,EAAQsD,YACVtD,EAAQsD,WAAWkI,YAAYxL,GAGjC4H,EAAasB,QAAQlJ,EA9DP,sBAmETyL,gBAAP,SAAuBtJ,GACrB,OAAOuF,KAAKgE,MAAK,WACf,IAAI/G,EAAOK,EAAa0C,KA5Eb,YA8EN/C,IACHA,EAAO,IAAIgG,EAAMjD,OAGJ,UAAXvF,GACFwC,EAAKxC,GAAQuF,YAKZiE,cAAP,SAAqBC,GACnB,OAAO,SAAUrF,GACXA,GACFA,EAAM4D,iBAGRyB,EAAchB,MAAMlD,iDAtEtB,MAxBa,iBAoBXiD,CAAcP,GAoFpBxC,EAAaQ,GAAGvI,SAhGU,0BAJD,4BAoGyC8K,EAAMgB,cAAc,IAAIhB,IAS1FzG,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAGoD,GAChCrB,EAAE/B,GAAGoD,GAAQC,EAAMc,gBACnBpC,EAAE/B,GAAGoD,GAAMoB,YAAcnB,EACzBtB,EAAE/B,GAAGoD,GAAMqB,WAAa,WAEtB,OADA1C,EAAE/B,GAAGoD,GAAQmB,EACNlB,EAAMc,qBClInB,IAiBMO,EAAAA,SAAAA,+EASJC,OAAA,WAEEvE,KAAK2C,SAAS6B,aAAa,eAAgBxE,KAAK2C,SAASe,UAAUa,OAvB7C,cA4BjBR,gBAAP,SAAuBtJ,GACrB,OAAOuF,KAAKgE,MAAK,WACf,IAAI/G,EAAOK,EAAa0C,KAlCb,aAoCN/C,IACHA,EAAO,IAAIqH,EAAOtE,OAGL,WAAXvF,GACFwC,EAAKxC,kDArBT,MApBa,kBAgBX6J,CAAe5B,GC5BrB,SAAS+B,EAAcC,GACrB,MAAY,SAARA,GAIQ,UAARA,IAIAA,IAAQrL,OAAOqL,GAAKxJ,WACf7B,OAAOqL,GAGJ,KAARA,GAAsB,SAARA,EACT,KAGFA,GAGT,SAASC,EAAiB3H,GACxB,OAAOA,EAAIoC,QAAQ,UAAU,SAAAwF,GAAG,MAAA,IAAQA,EAAIvJ,iBD4C9C6E,EAAaQ,GAAGvI,SA7CU,2BAFG,6BA+CyC,SAAA0G,GACpEA,EAAM4D,iBAEN,IAAMoC,EAAShG,EAAMkB,OAAOyD,QAlDD,6BAoDvBvG,EAAOK,EAAauH,EA1DT,aA2DV5H,IACHA,EAAO,IAAIqH,EAAOO,IAGpB5H,EAAKsH,YAUP/H,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAF,OAC3B+B,EAAE/B,GAAF,OAAa0E,EAAOP,gBACpBpC,EAAE/B,GAAF,OAAWwE,YAAcE,EAEzB3C,EAAE/B,GAAF,OAAWyE,WAAa,WAEtB,OADA1C,EAAE/B,GAAF,OAAauE,EACNG,EAAOP,qBCvEpB,IAAMe,EAAc,CAClBC,iBADkB,SACDzM,EAAS0E,EAAKhC,GAC7B1C,EAAQkM,aAAR,WAAgCG,EAAiB3H,GAAQhC,IAG3DgK,oBALkB,SAKE1M,EAAS0E,GAC3B1E,EAAQ2M,gBAAR,WAAmCN,EAAiB3H,KAGtDkI,kBATkB,SASA5M,GAChB,IAAKA,EACH,MAAO,GAGT,IAAM6M,EAAa,GAUnB,OARAxK,OAAOC,KAAKtC,EAAQ8M,SACjBC,QAAO,SAAArI,GAAG,OAAIA,EAAI+D,WAAW,SAC7BlG,SAAQ,SAAAmC,GACP,IAAIsI,EAAUtI,EAAIoC,QAAQ,MAAO,IACjCkG,EAAUA,EAAQC,OAAO,GAAGlK,cAAgBiK,EAAQhE,MAAM,EAAGgE,EAAQ1G,QACrEuG,EAAWG,GAAWb,EAAcnM,EAAQ8M,QAAQpI,OAGjDmI,GAGTK,iBA3BkB,SA2BDlN,EAAS0E,GACxB,OAAOyH,EAAcnM,EAAQE,aAAR,WAAgCmM,EAAiB3H,MAGxEyI,OA/BkB,SA+BXnN,GACL,IAAMoN,EAAOpN,EAAQqN,wBAErB,MAAO,CACLC,IAAKF,EAAKE,IAAMzN,SAASmE,KAAKuJ,UAC9BC,KAAMJ,EAAKI,KAAO3N,SAASmE,KAAKyJ,aAIpCC,SAxCkB,SAwCT1N,GACP,MAAO,CACLsN,IAAKtN,EAAQ2N,UACbH,KAAMxN,EAAQ4N,cC3DdC,EAAiB,CACrBC,QADqB,SACb9N,EAASC,GACf,OAAOD,EAAQ8N,QAAQ7N,IAGzB8N,KALqB,SAKhB9N,EAAUD,GAAoC,IAAAgO,EACjD,YADiD,IAApChO,IAAAA,EAAUH,SAASyE,kBACzB0J,EAAA,IAAGC,OAAHlG,MAAAiG,EAAaE,QAAQC,UAAU3G,iBAAiB3E,KAAK7C,EAASC,KAGvEmO,QATqB,SASbnO,EAAUD,GAChB,YADoD,IAApCA,IAAAA,EAAUH,SAASyE,iBAC5B4J,QAAQC,UAAU7N,cAAcuC,KAAK7C,EAASC,IAGvDoO,SAbqB,SAaZrO,EAASC,GAAU,IAAAqO,EACpBD,GAAWC,EAAA,IAAGL,OAAHlG,MAAAuG,EAAatO,EAAQqO,UAEtC,OAAOA,EAAStB,QAAO,SAAAwB,GAAK,OAAIA,EAAMT,QAAQ7N,OAGhDuO,QAnBqB,SAmBbxO,EAASC,GAKf,IAJA,IAAMuO,EAAU,GAEZC,EAAWzO,EAAQsD,WAEhBmL,GAAYA,EAASjN,WAAakN,KAAKC,cA1BhC,IA0BgDF,EAASjN,UACjEkG,KAAKoG,QAAQW,EAAUxO,IACzBuO,EAAQI,KAAKH,GAGfA,EAAWA,EAASnL,WAGtB,OAAOkL,GAGTK,KAnCqB,SAmChB7O,EAASC,GAGZ,IAFA,IAAI6O,EAAW9O,EAAQ+O,uBAEhBD,GAAU,CACf,GAAIA,EAAShB,QAAQ7N,GACnB,MAAO,CAAC6O,GAGVA,EAAWA,EAASC,uBAGtB,MAAO,IAGTC,KAjDqB,SAiDhBhP,EAASC,GAGZ,IAFA,IAAI+O,EAAOhP,EAAQiP,mBAEZD,GAAM,CACX,GAAItH,KAAKoG,QAAQkB,EAAM/O,GACrB,MAAO,CAAC+O,GAGVA,EAAOA,EAAKC,mBAGd,MAAO,KC5CLvE,EAAO,WAEPwE,EAAS,eAQTC,EAAU,CACdC,SAAU,IACVC,UAAU,EACVC,OAAO,EACPC,MAAO,QACPC,MAAM,EACNC,OAAO,GAGHC,EAAc,CAClBN,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,MAAO,mBACPC,KAAM,UACNC,MAAO,WAwCHE,GAAc,CAClBC,MAAO,QACPC,IAAK,OAQDC,GAAAA,SAAAA,GACJ,SAAAA,EAAY9P,EAASmC,GAAQ,IAAAgJ,EAAA,OAC3BA,EAAA4E,EAAAlN,KAAA6E,KAAM1H,IAAN0H,MAEKsI,OAAS,KACd7E,EAAK8E,UAAY,KACjB9E,EAAK+E,eAAiB,KACtB/E,EAAKgF,WAAY,EACjBhF,EAAKiF,YAAa,EAClBjF,EAAKkF,aAAe,KACpBlF,EAAKmF,YAAc,EACnBnF,EAAKoF,YAAc,EAEnBpF,EAAKqF,QAAUrF,EAAKsF,WAAWtO,GAC/BgJ,EAAKuF,mBAAqB7C,EAAeO,QA5BjB,uBA4B8CjD,EAAKd,UAC3Ec,EAAKwF,gBAAkB,iBAAkB9Q,SAASyE,iBAAmBsM,UAAUC,eAAiB,EAChG1F,EAAK2F,cAAgB3I,QAAQzH,OAAOqQ,cAEpC5F,EAAK6F,qBAjBsB7F,oCAgC7B6D,KAAA,WACOtH,KAAK0I,YACR1I,KAAKuJ,OAlFY,WAsFrBC,gBAAA,YAGOrR,SAASsR,QAAU/N,EAAUsE,KAAK2C,WACrC3C,KAAKsH,UAITH,KAAA,WACOnH,KAAK0I,YACR1I,KAAKuJ,OA/FY,WAmGrB1B,MAAA,SAAMhJ,GACCA,IACHmB,KAAKyI,WAAY,GAGftC,EAAeO,QAzEI,2CAyEwB1G,KAAK2C,YAClDlJ,EAAqBuG,KAAK2C,UAC1B3C,KAAK0J,OAAM,IAGbC,cAAc3J,KAAKuI,WACnBvI,KAAKuI,UAAY,QAGnBmB,MAAA,SAAM7K,GACCA,IACHmB,KAAKyI,WAAY,GAGfzI,KAAKuI,YACPoB,cAAc3J,KAAKuI,WACnBvI,KAAKuI,UAAY,MAGfvI,KAAK8I,SAAW9I,KAAK8I,QAAQpB,WAAa1H,KAAKyI,YACjDzI,KAAK4J,kBAEL5J,KAAKuI,UAAYsB,aACd1R,SAAS2R,gBAAkB9J,KAAKwJ,gBAAkBxJ,KAAKsH,MAAMyC,KAAK/J,MACnEA,KAAK8I,QAAQpB,cAKnBsC,GAAA,SAAGC,GAAO,IAAAC,EAAAlK,KACRA,KAAKwI,eAAiBrC,EAAeO,QA1GZ,wBA0G0C1G,KAAK2C,UACxE,IAAMwH,EAAcnK,KAAKoK,cAAcpK,KAAKwI,gBAE5C,KAAIyB,EAAQjK,KAAKsI,OAAO1J,OAAS,GAAKqL,EAAQ,GAI9C,GAAIjK,KAAK0I,WACPxI,EAAaS,IAAIX,KAAK2C,SAzIZ,oBAyIkC,WAAA,OAAMuH,EAAKF,GAAGC,UAD5D,CAKA,GAAIE,IAAgBF,EAGlB,OAFAjK,KAAK6H,aACL7H,KAAK0J,QAIP,IAAMW,EAAYJ,EAAQE,EAzJP,OACA,OA4JnBnK,KAAKuJ,OAAOc,EAAWrK,KAAKsI,OAAO2B,QAGrCnH,QAAA,WACEuF,EAAA5B,UAAM3D,QAAN3H,KAAA6E,MACAE,EAAaC,IAAIH,KAAK2C,SAAU6E,GAEhCxH,KAAKsI,OAAS,KACdtI,KAAK8I,QAAU,KACf9I,KAAKuI,UAAY,KACjBvI,KAAKyI,UAAY,KACjBzI,KAAK0I,WAAa,KAClB1I,KAAKwI,eAAiB,KACtBxI,KAAKgJ,mBAAqB,QAK5BD,WAAA,SAAWtO,GAMT,OALAA,EAAM6P,EAAA,GACD7C,EACAhN,GAELF,EAAgByI,EAAMvI,EAAQuN,GACvBvN,KAGT8P,aAAA,WACE,IAAMC,EAAYxS,KAAKyS,IAAIzK,KAAK6I,aAEhC,KAAI2B,GA/MgB,IA+MpB,CAIA,IAAMH,EAAYG,EAAYxK,KAAK6I,YAEnC7I,KAAK6I,YAAc,EAGfwB,EAAY,GACdrK,KAAKmH,OAIHkD,EAAY,GACdrK,KAAKsH,WAITgC,mBAAA,WAAqB,IAAAoB,EAAA1K,KACfA,KAAK8I,QAAQnB,UACfzH,EAAaQ,GAAGV,KAAK2C,SAzMR,uBAyMiC,SAAA9D,GAAK,OAAI6L,EAAKC,SAAS9L,MAG5C,UAAvBmB,KAAK8I,QAAQjB,QACf3H,EAAaQ,GAAGV,KAAK2C,SA5ML,0BA4MiC,SAAA9D,GAAK,OAAI6L,EAAK7C,MAAMhJ,MACrEqB,EAAaQ,GAAGV,KAAK2C,SA5ML,0BA4MiC,SAAA9D,GAAK,OAAI6L,EAAKhB,MAAM7K,OAGnEmB,KAAK8I,QAAQf,OAAS/H,KAAKiJ,iBAC7BjJ,KAAK4K,6BAITA,wBAAA,WAA0B,IAAAC,EAAA7K,KAClB8K,EAAQ,SAAAjM,GACRgM,EAAKzB,eAAiBnB,GAAYpJ,EAAMkM,YAAYtP,eACtDoP,EAAKjC,YAAc/J,EAAMmM,QACfH,EAAKzB,gBACfyB,EAAKjC,YAAc/J,EAAMoM,QAAQ,GAAGD,UAalCE,EAAM,SAAArM,GACNgM,EAAKzB,eAAiBnB,GAAYpJ,EAAMkM,YAAYtP,iBACtDoP,EAAKhC,YAAchK,EAAMmM,QAAUH,EAAKjC,aAG1CiC,EAAKN,eACsB,UAAvBM,EAAK/B,QAAQjB,QASfgD,EAAKhD,QACDgD,EAAKlC,cACPwC,aAAaN,EAAKlC,cAGpBkC,EAAKlC,aAAerO,YAAW,SAAAuE,GAAK,OAAIgM,EAAKnB,MAAM7K,KAxR5B,IAwR6DgM,EAAK/B,QAAQpB,YAIrGvB,EAAeE,KAxOO,qBAwOiBrG,KAAK2C,UAAU9H,SAAQ,SAAAuQ,GAC5DlL,EAAaQ,GAAG0K,EAzPA,yBAyP2B,SAAAC,GAAC,OAAIA,EAAE5I,uBAGhDzC,KAAKoJ,eACPlJ,EAAaQ,GAAGV,KAAK2C,SA/PJ,2BA+PiC,SAAA9D,GAAK,OAAIiM,EAAMjM,MACjEqB,EAAaQ,GAAGV,KAAK2C,SA/PN,yBA+PiC,SAAA9D,GAAK,OAAIqM,EAAIrM,MAE7DmB,KAAK2C,SAASe,UAAU4H,IArPG,mBAuP3BpL,EAAaQ,GAAGV,KAAK2C,SAvQL,0BAuQiC,SAAA9D,GAAK,OAAIiM,EAAMjM,MAChEqB,EAAaQ,GAAGV,KAAK2C,SAvQN,yBAuQiC,SAAA9D,GAAK,OA5C1C,SAAAA,GAEPA,EAAMoM,SAAWpM,EAAMoM,QAAQrM,OAAS,EAC1CiM,EAAKhC,YAAc,EAEnBgC,EAAKhC,YAAchK,EAAMoM,QAAQ,GAAGD,QAAUH,EAAKjC,YAuCI2C,CAAK1M,MAC9DqB,EAAaQ,GAAGV,KAAK2C,SAvQP,wBAuQiC,SAAA9D,GAAK,OAAIqM,EAAIrM,UAIhE8L,SAAA,SAAS9L,GACP,IAAI,kBAAkBtD,KAAKsD,EAAMkB,OAAOyL,SAIxC,OAAQ3M,EAAM7B,KACZ,IApTiB,YAqTf6B,EAAM4D,iBACNzC,KAAKmH,OACL,MACF,IAvTkB,aAwThBtI,EAAM4D,iBACNzC,KAAKsH,WAMX8C,cAAA,SAAc9R,GAKZ,OAJA0H,KAAKsI,OAAShQ,GAAWA,EAAQsD,WAC/BuK,EAAeE,KA7QC,iBA6QmB/N,EAAQsD,YAC3C,GAEKoE,KAAKsI,OAAOmD,QAAQnT,MAG7BoT,oBAAA,SAAoBrB,EAAWsB,GAC7B,IAAMC,EAlTa,SAkTKvB,EAClBwB,EAlTa,SAkTKxB,EAClBF,EAAcnK,KAAKoK,cAAcuB,GACjCG,EAAgB9L,KAAKsI,OAAO1J,OAAS,EAI3C,IAHuBiN,GAAmC,IAAhB1B,GACjByB,GAAmBzB,IAAgB2B,KAEtC9L,KAAK8I,QAAQhB,KACjC,OAAO6D,EAGT,IACMI,GAAa5B,GA7TA,SA4TLE,GAAgC,EAAI,IACRrK,KAAKsI,OAAO1J,OAEtD,OAAsB,IAAfmN,EACL/L,KAAKsI,OAAOtI,KAAKsI,OAAO1J,OAAS,GACjCoB,KAAKsI,OAAOyD,MAGhBC,mBAAA,SAAmBC,EAAeC,GAChC,IAAMC,EAAcnM,KAAKoK,cAAc6B,GACjCG,EAAYpM,KAAKoK,cAAcjE,EAAeO,QA1S3B,wBA0SyD1G,KAAK2C,WAEvF,OAAOzC,EAAasB,QAAQxB,KAAK2C,SApUpB,oBAoU2C,CACtDsJ,cAAAA,EACA5B,UAAW6B,EACXG,KAAMD,EACNpC,GAAImC,OAIRG,2BAAA,SAA2BhU,GACzB,GAAI0H,KAAKgJ,mBAAoB,CAG3B,IAFA,IAAMuD,EAAapG,EAAeE,KAvThB,UAuTsCrG,KAAKgJ,oBAEpDtK,EAAI,EAAGA,EAAI6N,EAAW3N,OAAQF,IACrC6N,EAAW7N,GAAGgF,UAAUC,OAlUN,UAqUpB,IAAM6I,EAAgBxM,KAAKgJ,mBAAmBrC,SAC5C3G,KAAKoK,cAAc9R,IAGjBkU,GACFA,EAAc9I,UAAU4H,IA1UN,cA+UxB1B,gBAAA,WACE,IAAMtR,EAAU0H,KAAKwI,gBAAkBrC,EAAeO,QAvU7B,wBAuU2D1G,KAAK2C,UAEzF,GAAKrK,EAAL,CAIA,IAAMmU,EAAkBpT,OAAOqT,SAASpU,EAAQE,aAAa,oBAAqB,IAE9EiU,GACFzM,KAAK8I,QAAQ6D,gBAAkB3M,KAAK8I,QAAQ6D,iBAAmB3M,KAAK8I,QAAQpB,SAC5E1H,KAAK8I,QAAQpB,SAAW+E,GAExBzM,KAAK8I,QAAQpB,SAAW1H,KAAK8I,QAAQ6D,iBAAmB3M,KAAK8I,QAAQpB,aAIzE6B,OAAA,SAAOc,EAAW/R,GAAS,IAQrBsU,EACAC,EACAX,EAVqBY,EAAA9M,KACnB2L,EAAgBxF,EAAeO,QAxVZ,wBAwV0C1G,KAAK2C,UAClEoK,EAAqB/M,KAAKoK,cAAcuB,GACxCqB,EAAc1U,GAAYqT,GAAiB3L,KAAK0L,oBAAoBrB,EAAWsB,GAE/EsB,EAAmBjN,KAAKoK,cAAc4C,GACtCE,EAAYzM,QAAQT,KAAKuI,WAgB/B,GA1YmB,SAgYf8B,GACFuC,EA1WmB,sBA2WnBC,EA1WkB,qBA2WlBX,EAjYiB,SAmYjBU,EA/WiB,oBAgXjBC,EA7WkB,qBA8WlBX,EApYkB,SAuYhBc,GAAeA,EAAYtJ,UAAUE,SAtXnB,UAuXpB5D,KAAK0I,YAAa,OAKpB,IADmB1I,KAAKgM,mBAAmBgB,EAAad,GACzCnK,kBAIV4J,GAAkBqB,EAAvB,CAcA,GATAhN,KAAK0I,YAAa,EAEdwE,GACFlN,KAAK6H,QAGP7H,KAAKsM,2BAA2BU,GAChChN,KAAKwI,eAAiBwE,EAElBhN,KAAK2C,SAASe,UAAUE,SA7YP,SA6YmC,CACtDoJ,EAAYtJ,UAAU4H,IAAIuB,GAE1B3Q,EAAO8Q,GAEPrB,EAAcjI,UAAU4H,IAAIsB,GAC5BI,EAAYtJ,UAAU4H,IAAIsB,GAE1B,IAAM1T,EAAqBJ,EAAiC6S,GAE5DzL,EAAaS,IAAIgL,ER9dA,iBQ8d+B,WAC9CqB,EAAYtJ,UAAUC,OAAOiJ,EAAsBC,GACnDG,EAAYtJ,UAAU4H,IA1ZJ,UA4ZlBK,EAAcjI,UAAUC,OA5ZN,SA4ZgCkJ,EAAgBD,GAElEE,EAAKpE,YAAa,EAElBpO,YAAW,WACT4F,EAAasB,QAAQsL,EAAKnK,SA/apB,mBA+a0C,CAC9CsJ,cAAee,EACf3C,UAAW6B,EACXG,KAAMU,EACN/C,GAAIiD,MAEL,MAGLlT,EAAqB4R,EAAezS,QAEpCyS,EAAcjI,UAAUC,OA5aJ,UA6apBqJ,EAAYtJ,UAAU4H,IA7aF,UA+apBtL,KAAK0I,YAAa,EAClBxI,EAAasB,QAAQxB,KAAK2C,SA9bhB,mBA8bsC,CAC9CsJ,cAAee,EACf3C,UAAW6B,EACXG,KAAMU,EACN/C,GAAIiD,IAIJC,GACFlN,KAAK0J,YAMFyD,kBAAP,SAAyB7U,EAASmC,GAChC,IAAIwC,EAAOK,EAAahF,EA/eX,eAgfTwQ,EAAOwB,EAAA,GACN7C,EACA3C,EAAYI,kBAAkB5M,IAGb,iBAAXmC,IACTqO,EAAOwB,EAAA,GACFxB,EACArO,IAIP,IAAM2S,EAA2B,iBAAX3S,EAAsBA,EAASqO,EAAQlB,MAM7D,GAJK3K,IACHA,EAAO,IAAImL,EAAS9P,EAASwQ,IAGT,iBAAXrO,EACTwC,EAAK+M,GAAGvP,QACH,GAAsB,iBAAX2S,EAAqB,CACrC,QAA4B,IAAjBnQ,EAAKmQ,GACd,MAAM,IAAIC,UAAJ,oBAAkCD,EAAlC,KAGRnQ,EAAKmQ,UACItE,EAAQpB,UAAYoB,EAAQwE,OACrCrQ,EAAK4K,QACL5K,EAAKyM,YAIF3F,gBAAP,SAAuBtJ,GACrB,OAAOuF,KAAKgE,MAAK,WACfoE,EAAS+E,kBAAkBnN,KAAMvF,SAI9B8S,oBAAP,SAA2B1O,GACzB,IAAMkB,EAASlH,EAAuBmH,MAEtC,GAAKD,GAAWA,EAAO2D,UAAUE,SA3eT,YA2exB,CAIA,IAAMnJ,EAAM6P,EAAA,GACPxF,EAAYI,kBAAkBnF,GAC9B+E,EAAYI,kBAAkBlF,OAE7BwN,EAAaxN,KAAKxH,aAAa,oBAEjCgV,IACF/S,EAAOiN,UAAW,GAGpBU,EAAS+E,kBAAkBpN,EAAQtF,GAE/B+S,GACFlQ,EAAayC,EA1iBF,eA0iBoBiK,GAAGwD,GAGpC3O,EAAM4D,2DA3cN,OAAOgF,mCAIP,MAtGa,oBA0EXW,CAAiB1F,GA6evBxC,EAAaQ,GAAGvI,SA3gBU,6BAiBE,sCA0fyCiQ,GAASmF,qBAE9ErN,EAAaQ,GAAG1H,OA9gBS,6BA8gBoB,WAG3C,IAFA,IAAMyU,EAAYtH,EAAeE,KA5fR,6BA8fhB3H,EAAI,EAAGC,EAAM8O,EAAU7O,OAAQF,EAAIC,EAAKD,IAC/C0J,GAAS+E,kBAAkBM,EAAU/O,GAAIpB,EAAamQ,EAAU/O,GA7jBnD,mBAwkBjBlC,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAGoD,GAChCrB,EAAE/B,GAAGoD,GAAQoF,GAASrE,gBACtBpC,EAAE/B,GAAGoD,GAAMoB,YAAcgE,GACzBzG,EAAE/B,GAAGoD,GAAMqB,WAAa,WAEtB,OADA1C,EAAE/B,GAAGoD,GAAQmB,EACNiE,GAASrE,qBCllBtB,IAAMf,GAAO,WAKPyE,GAAU,CACdlD,QAAQ,EACRmJ,OAAQ,IAGJ1F,GAAc,CAClBzD,OAAQ,UACRmJ,OAAQ,oBA0BJC,GAAAA,SAAAA,GACJ,SAAAA,EAAYrV,EAASmC,GAAQ,IAAAgJ,GAC3BA,EAAA4E,EAAAlN,KAAA6E,KAAM1H,IAAN0H,MAEK4N,kBAAmB,EACxBnK,EAAKqF,QAAUrF,EAAKsF,WAAWtO,GAC/BgJ,EAAKoK,cAAgB1H,EAAeE,KAC/ByH,sCAA+BxV,EAAQT,GAAvCiW,mDACyCxV,EAAQT,GADpD,MAMF,IAFA,IAAMkW,EAAa5H,EAAeE,KAnBT,+BAqBhB3H,EAAI,EAAGC,EAAMoP,EAAWnP,OAAQF,EAAIC,EAAKD,IAAK,CACrD,IAAMsP,EAAOD,EAAWrP,GAClBnG,EAAWI,EAAuBqV,GAClCC,EAAgB9H,EAAeE,KAAK9N,GACvC8M,QAAO,SAAA6I,GAAS,OAAIA,IAAc5V,KAEpB,OAAbC,GAAqB0V,EAAcrP,SACrC6E,EAAK0K,UAAY5V,EACjBkL,EAAKoK,cAAc3G,KAAK8G,IApBD,OAwB3BvK,EAAK2K,QAAU3K,EAAKqF,QAAQ4E,OAASjK,EAAK4K,aAAe,KAEpD5K,EAAKqF,QAAQ4E,QAChBjK,EAAK6K,0BAA0B7K,EAAKd,SAAUc,EAAKoK,eAGjDpK,EAAKqF,QAAQvE,QACfd,EAAKc,SA/BoBd,oCA+C7Bc,OAAA,WACMvE,KAAK2C,SAASe,UAAUE,SAlER,QAmElB5D,KAAKuO,OAELvO,KAAKwO,UAITA,KAAA,WAAO,IAAAtE,EAAAlK,KACL,IAAIA,KAAK4N,mBAAoB5N,KAAK2C,SAASe,UAAUE,SA1EjC,QA0EpB,CAIA,IAAI6K,EACAC,EAEA1O,KAAKoO,SAUgB,KATvBK,EAAUtI,EAAeE,KA1EN,qBA0E6BrG,KAAKoO,SAClD/I,QAAO,SAAA2I,GACN,MAAmC,iBAAxB9D,EAAKpB,QAAQ4E,OACfM,EAAKxV,aAAa,oBAAsB0R,EAAKpB,QAAQ4E,OAGvDM,EAAKtK,UAAUE,SAvFJ,gBA0FVhF,SACV6P,EAAU,MAId,IAAME,EAAYxI,EAAeO,QAAQ1G,KAAKmO,WAC9C,GAAIM,EAAS,CACX,IAAMG,EAAiBH,EAAQpI,MAAK,SAAA2H,GAAI,OAAIW,IAAcX,KAG1D,IAFAU,EAAcE,EAAiBtR,EAAasR,EAvHjC,eAuH6D,OAErDF,EAAYd,iBAC7B,OAKJ,IADmB1N,EAAasB,QAAQxB,KAAK2C,SAhHjC,oBAiHGZ,iBAAf,CAII0M,GACFA,EAAQ5T,SAAQ,SAAAgU,GACVF,IAAcE,GAChBlB,EAASmB,kBAAkBD,EAAY,QAGpCH,GACHpR,EAAauR,EA1IN,cA0I4B,SAKzC,IAAME,EAAY/O,KAAKgP,gBAEvBhP,KAAK2C,SAASe,UAAUC,OA5HA,YA6HxB3D,KAAK2C,SAASe,UAAU4H,IA5HE,cA8H1BtL,KAAK2C,SAAShH,MAAMoT,GAAa,EAE7B/O,KAAK6N,cAAcjP,QACrBoB,KAAK6N,cAAchT,SAAQ,SAAAvC,GACzBA,EAAQoL,UAAUC,OAjIG,aAkIrBrL,EAAQkM,aAAa,iBAAiB,MAI1CxE,KAAKiP,kBAAiB,GAEtB,IAYMC,EAAU,UADaH,EAAU,GAAGtT,cAAgBsT,EAAUzN,MAAM,IAEpEpI,EAAqBJ,EAAiCkH,KAAK2C,UAEjEzC,EAAaS,IAAIX,KAAK2C,STrMH,iBSsLF,WACfuH,EAAKvH,SAASe,UAAUC,OA1IA,cA2IxBuG,EAAKvH,SAASe,UAAU4H,IA5IF,WADJ,QA+IlBpB,EAAKvH,SAAShH,MAAMoT,GAAa,GAEjC7E,EAAK+E,kBAAiB,GAEtB/O,EAAasB,QAAQ0I,EAAKvH,SAxJf,wBAiKb5I,EAAqBiG,KAAK2C,SAAUzJ,GACpC8G,KAAK2C,SAAShH,MAAMoT,GAAgB/O,KAAK2C,SAASuM,GAAlD,UAGFX,KAAA,WAAO,IAAA7D,EAAA1K,KACL,IAAIA,KAAK4N,kBAAqB5N,KAAK2C,SAASe,UAAUE,SAjKlC,UAqKD1D,EAAasB,QAAQxB,KAAK2C,SAzKjC,oBA0KGZ,iBAAf,CAIA,IAAMgN,EAAY/O,KAAKgP,gBAEvBhP,KAAK2C,SAAShH,MAAMoT,GAAgB/O,KAAK2C,SAASgD,wBAAwBoJ,GAA1E,KAEA7S,EAAO8D,KAAK2C,UAEZ3C,KAAK2C,SAASe,UAAU4H,IA9KE,cA+K1BtL,KAAK2C,SAASe,UAAUC,OAhLA,WADJ,QAmLpB,IAAMwL,EAAqBnP,KAAK6N,cAAcjP,OAC9C,GAAIuQ,EAAqB,EACvB,IAAK,IAAIzQ,EAAI,EAAGA,EAAIyQ,EAAoBzQ,IAAK,CAC3C,IAAM8C,EAAUxB,KAAK6N,cAAcnP,GAC7BsP,EAAOnV,EAAuB2I,GAEhCwM,IAASA,EAAKtK,UAAUE,SAzLZ,UA0LdpC,EAAQkC,UAAU4H,IAvLC,aAwLnB9J,EAAQgD,aAAa,iBAAiB,IAK5CxE,KAAKiP,kBAAiB,GAStBjP,KAAK2C,SAAShH,MAAMoT,GAAa,GACjC,IAAM7V,EAAqBJ,EAAiCkH,KAAK2C,UAEjEzC,EAAaS,IAAIX,KAAK2C,STvPH,iBS6OF,WACf+H,EAAKuE,kBAAiB,GACtBvE,EAAK/H,SAASe,UAAUC,OAlMA,cAmMxB+G,EAAK/H,SAASe,UAAU4H,IApMF,YAqMtBpL,EAAasB,QAAQkJ,EAAK/H,SAzMd,yBAgNd5I,EAAqBiG,KAAK2C,SAAUzJ,OAGtC+V,iBAAA,SAAiBG,GACfpP,KAAK4N,iBAAmBwB,KAG1BtM,QAAA,WACEuF,EAAA5B,UAAM3D,QAAN3H,KAAA6E,MACAA,KAAK8I,QAAU,KACf9I,KAAKoO,QAAU,KACfpO,KAAK6N,cAAgB,KACrB7N,KAAK4N,iBAAmB,QAK1B7E,WAAA,SAAWtO,GAOT,OANAA,EAAM6P,EAAA,GACD7C,GACAhN,IAEE8J,OAAS9D,QAAQhG,EAAO8J,QAC/BhK,EAAgByI,GAAMvI,EAAQuN,IACvBvN,KAGTuU,cAAA,WACE,OAAOhP,KAAK2C,SAASe,UAAUE,SApOrB,SAAA,QACC,YAsObyK,WAAA,WAAa,IAAAxD,EAAA7K,KACL0N,EAAW1N,KAAK8I,QAAhB4E,OAEF9T,EAAU8T,QAEiB,IAAlBA,EAAO2B,aAA+C,IAAd3B,EAAO,KACxDA,EAASA,EAAO,IAGlBA,EAASvH,EAAeO,QAAQgH,GAGlC,IAAMnV,EAAcuV,+CAAwCJ,EAA9C,KAYd,OAVAvH,EAAeE,KAAK9N,EAAUmV,GAC3B7S,SAAQ,SAAAvC,GACP,IAAMgX,EAAWzW,EAAuBP,GAExCuS,EAAKyD,0BACHgB,EACA,CAAChX,OAIAoV,KAGTY,0BAAA,SAA0BhW,EAASiX,GACjC,GAAKjX,GAAYiX,EAAa3Q,OAA9B,CAIA,IAAM4Q,EAASlX,EAAQoL,UAAUE,SA5Qb,QA8QpB2L,EAAa1U,SAAQ,SAAAmT,GACfwB,EACFxB,EAAKtK,UAAUC,OA7QM,aA+QrBqK,EAAKtK,UAAU4H,IA/QM,aAkRvB0C,EAAKxJ,aAAa,gBAAiBgL,UAMhCV,kBAAP,SAAyBxW,EAASmC,GAChC,IAAIwC,EAAOK,EAAahF,EAhTX,eAiTPwQ,EAAOwB,EAAA,GACR7C,GACA3C,EAAYI,kBAAkB5M,GACX,iBAAXmC,GAAuBA,EAASA,EAAS,IAWtD,IARKwC,GAAQ6L,EAAQvE,QAA4B,iBAAX9J,GAAuB,YAAYc,KAAKd,KAC5EqO,EAAQvE,QAAS,GAGdtH,IACHA,EAAO,IAAI0Q,EAASrV,EAASwQ,IAGT,iBAAXrO,EAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAI4S,UAAJ,oBAAkC5S,EAAlC,KAGRwC,EAAKxC,SAIFsJ,gBAAP,SAAuBtJ,GACrB,OAAOuF,KAAKgE,MAAK,WACf2J,EAASmB,kBAAkB9O,KAAMvF,+CA9PnC,OAAOgN,oCAIP,MAhFa,oBAqCXkG,CAAiBjL,GAgTvBxC,EAAaQ,GAAGvI,SAnUU,6BAWG,+BAwTyC,SAAU0G,GAEjD,MAAzBA,EAAMkB,OAAOyL,SACf3M,EAAM4D,iBAGR,IAAMgN,EAAc3K,EAAYI,kBAAkBlF,MAC5CzH,EAAWI,EAAuBqH,MACfmG,EAAeE,KAAK9N,GAE5BsC,SAAQ,SAAAvC,GACvB,IACImC,EADEwC,EAAOK,EAAahF,EAhWb,eAkWT2E,GAEmB,OAAjBA,EAAKmR,SAAkD,iBAAvBqB,EAAY/B,SAC9CzQ,EAAK6L,QAAQ4E,OAAS+B,EAAY/B,OAClCzQ,EAAKmR,QAAUnR,EAAKoR,cAGtB5T,EAAS,UAETA,EAASgV,EAGX9B,GAASmB,kBAAkBxW,EAASmC,SAWxC+B,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAGoD,IAChCrB,EAAE/B,GAAGoD,IAAQ2K,GAAS5J,gBACtBpC,EAAE/B,GAAGoD,IAAMoB,YAAcuJ,GACzBhM,EAAE/B,GAAGoD,IAAMqB,WAAa,WAEtB,OADA1C,EAAE/B,GAAGoD,IAAQmB,EACNwJ,GAAS5J,qBCnYtB,IAAMf,GAAO,WAYP0M,GAAiB,IAAIpU,OAAUqU,4BAwB/BC,GAAgBjT,EAAQ,UAAY,YACpCkT,GAAmBlT,EAAQ,YAAc,UACzCmT,GAAmBnT,EAAQ,aAAe,eAC1CoT,GAAsBpT,EAAQ,eAAiB,aAC/CqT,GAAkBrT,EAAQ,aAAe,cACzCsT,GAAiBtT,EAAQ,cAAgB,aAEzC8K,GAAU,CACdhC,OAAQ,EACRyK,MAAM,EACNC,SAAU,kBACVC,UAAW,SACXrU,QAAS,UACTsU,aAAc,MAGVrI,GAAc,CAClBvC,OAAQ,2BACRyK,KAAM,UACNC,SAAU,mBACVC,UAAW,mBACXrU,QAAS,SACTsU,aAAc,iBASVC,GAAAA,SAAAA,GACJ,SAAAA,EAAYhY,EAASmC,GAAQ,IAAAgJ,EAAA,OAC3BA,EAAA4E,EAAAlN,KAAA6E,KAAM1H,IAAN0H,MAEKuQ,QAAU,KACf9M,EAAKqF,QAAUrF,EAAKsF,WAAWtO,GAC/BgJ,EAAK+M,MAAQ/M,EAAKgN,kBAClBhN,EAAKiN,UAAYjN,EAAKkN,gBAEtBlN,EAAK6F,qBARsB7F,oCA2B7Bc,OAAA,WACE,IAAIvE,KAAK2C,SAASiO,WAAY5Q,KAAK2C,SAASe,UAAUE,SAzE9B,YAyExB,CAIA,IAAMiN,EAAW7Q,KAAK2C,SAASe,UAAUE,SA5ErB,QA8EpB0M,EAASQ,aAELD,GAIJ7Q,KAAKwO,WAGPA,KAAA,WACE,KAAIxO,KAAK2C,SAASiO,UAAY5Q,KAAK2C,SAASe,UAAUE,SAzF9B,aAyF+D5D,KAAKwQ,MAAM9M,UAAUE,SAxFxF,SAwFpB,CAIA,IAAM8J,EAAS4C,EAASS,qBAAqB/Q,KAAK2C,UAC5CsJ,EAAgB,CACpBA,cAAejM,KAAK2C,UAKtB,IAFkBzC,EAAasB,QAAQxB,KAAK2C,SAzGhC,mBAyGsDsJ,GAEpDlK,iBAAd,CAKA,IAAK/B,KAAK0Q,UAAW,CACnB,QAAsB,IAAXM,EACT,MAAM,IAAI3D,UAAU,gEAGtB,IAAI4D,EAAmBjR,KAAK2C,SAEG,WAA3B3C,KAAK8I,QAAQsH,UACfa,EAAmBvD,EACV9T,EAAUoG,KAAK8I,QAAQsH,aAChCa,EAAmBjR,KAAK8I,QAAQsH,eAGa,IAAlCpQ,KAAK8I,QAAQsH,UAAUf,SAChC4B,EAAmBjR,KAAK8I,QAAQsH,UAAU,KAI9CpQ,KAAKuQ,QAAUS,EAAAA,aAAoBC,EAAkBjR,KAAKwQ,MAAOxQ,KAAKkR,oBAQhC,IAAA5K,EADxC,GAAI,iBAAkBnO,SAASyE,kBAC5B8Q,EAAOlK,QAzHc,gBA0HtB8C,EAAA,IAAGC,OAAHlG,MAAAiG,EAAanO,SAASmE,KAAKqK,UACxB9L,SAAQ,SAAAmT,GAAI,OAAI9N,EAAaQ,GAAGsN,EAAM,YAAa,MVrBzC,kBUwBfhO,KAAK2C,SAASwO,QACdnR,KAAK2C,SAAS6B,aAAa,iBAAiB,GAE5CxE,KAAKwQ,MAAM9M,UAAUa,OA1ID,QA2IpBvE,KAAK2C,SAASe,UAAUa,OA3IJ,QA4IpBrE,EAAasB,QAAQkM,EAnJR,oBAmJ6BzB,QAG5CsC,KAAA,WACE,IAAIvO,KAAK2C,SAASiO,WAAY5Q,KAAK2C,SAASe,UAAUE,SAjJ9B,aAiJgE5D,KAAKwQ,MAAM9M,UAAUE,SAhJzF,QAgJpB,CAIA,IAAM8J,EAAS4C,EAASS,qBAAqB/Q,KAAK2C,UAC5CsJ,EAAgB,CACpBA,cAAejM,KAAK2C,UAGJzC,EAAasB,QAAQkM,EAnK3B,mBAmK+CzB,GAE7ClK,mBAIV/B,KAAKuQ,SACPvQ,KAAKuQ,QAAQa,UAGfpR,KAAKwQ,MAAM9M,UAAUa,OAnKD,QAoKpBvE,KAAK2C,SAASe,UAAUa,OApKJ,QAqKpBrE,EAAasB,QAAQkM,EA9KP,qBA8K6BzB,QAG7CnJ,QAAA,WACEuF,EAAA5B,UAAM3D,QAAN3H,KAAA6E,MACAE,EAAaC,IAAIH,KAAK2C,SAhMX,gBAiMX3C,KAAKwQ,MAAQ,KAETxQ,KAAKuQ,UACPvQ,KAAKuQ,QAAQa,UACbpR,KAAKuQ,QAAU,SAInBc,OAAA,WACErR,KAAK0Q,UAAY1Q,KAAK2Q,gBAClB3Q,KAAKuQ,SACPvQ,KAAKuQ,QAAQc,YAMjB/H,mBAAA,WAAqB,IAAAY,EAAAlK,KACnBE,EAAaQ,GAAGV,KAAK2C,SAnMR,qBAmM+B,SAAA9D,GAC1CA,EAAM4D,iBACN5D,EAAMyS,kBACNpH,EAAK3F,eAITwE,WAAA,SAAWtO,GAST,OARAA,EAAM6P,EAAA,GACDtK,KAAK4C,YAAY6E,QACjB3C,EAAYI,kBAAkBlF,KAAK2C,UACnClI,GAGLF,EAAgByI,GAAMvI,EAAQuF,KAAK4C,YAAYoF,aAExCvN,KAGTgW,gBAAA,WACE,OAAOtK,EAAemB,KAAKtH,KAAK2C,SAzMd,kBAyMuC,MAG3D4O,cAAA,WACE,IAAMC,EAAiBxR,KAAK2C,SAAS/G,WAErC,GAAI4V,EAAe9N,UAAUE,SArNN,WAsNrB,OAAOoM,GAGT,GAAIwB,EAAe9N,UAAUE,SAxNJ,aAyNvB,OAAOqM,GAIT,IAAMwB,EAAkF,QAA1ExY,iBAAiB+G,KAAKwQ,OAAOkB,iBAAiB,iBAAiBhZ,OAE7E,OAAI8Y,EAAe9N,UAAUE,SAjOP,UAkOb6N,EAAQ5B,GAAmBD,GAG7B6B,EAAQ1B,GAAsBD,MAGvCa,cAAA,WACE,OAA0D,OAAnD3Q,KAAK2C,SAASa,QAAd,cAGT0N,iBAAA,WACE,IAAMb,EAAe,CACnBsB,UAAW3R,KAAKuR,gBAChBK,UAAW,CAAC,CACVC,KAAM,kBACNC,QAAS,CACPC,YAAa/R,KAAK8I,QAAQoH,KAC1B8B,aAAchS,KAAK8I,QAAQqH,aAajC,MAP6B,WAAzBnQ,KAAK8I,QAAQ/M,UACfsU,EAAauB,UAAY,CAAC,CACxBC,KAAM,cACNI,SAAS,KAIb3H,EAAA,GACK+F,EACArQ,KAAK8I,QAAQuH,iBAMb6B,kBAAP,SAAyB5Z,EAASmC,GAChC,IAAIwC,EAAOK,EAAahF,EAjSX,eAwSb,GAJK2E,IACHA,EAAO,IAAIqT,EAAShY,EAHY,iBAAXmC,EAAsBA,EAAS,OAMhC,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAI4S,UAAJ,oBAAkC5S,EAAlC,KAGRwC,EAAKxC,SAIFsJ,gBAAP,SAAuBtJ,GACrB,OAAOuF,KAAKgE,MAAK,WACfsM,EAAS4B,kBAAkBlS,KAAMvF,SAI9BqW,WAAP,SAAkBjS,GAChB,IAAIA,GA/SmB,IA+STA,EAAMgG,SAAiD,UAAfhG,EAAMuB,MAlThD,QAkToEvB,EAAM7B,KAMtF,IAFA,IAAMmV,EAAUhM,EAAeE,KA/RN,+BAiShB3H,EAAI,EAAGC,EAAMwT,EAAQvT,OAAQF,EAAIC,EAAKD,IAAK,CAClD,IAAMgP,EAAS4C,EAASS,qBAAqBoB,EAAQzT,IAC/C0T,EAAU9U,EAAa6U,EAAQzT,GAhU1B,eAiULuN,EAAgB,CACpBA,cAAekG,EAAQzT,IAOzB,GAJIG,GAAwB,UAAfA,EAAMuB,OACjB6L,EAAcoG,WAAaxT,GAGxBuT,EAAL,CAIA,IAAME,EAAeF,EAAQ5B,MAC7B,GAAK2B,EAAQzT,GAAGgF,UAAUE,SAvTR,QA2TlB,KAAI/E,IAA0B,UAAfA,EAAMuB,MACjB,kBAAkB7E,KAAKsD,EAAMkB,OAAOyL,UACpB,UAAf3M,EAAMuB,MA9UD,QA8UqBvB,EAAM7B,MACjCsV,EAAa1O,SAAS/E,EAAMkB,SAKhC,IADkBG,EAAasB,QAAQkM,EA5U7B,mBA4UiDzB,GAC7ClK,iBAAd,CAMgD,IAAA6E,EAAhD,GAAI,iBAAkBzO,SAASyE,iBAC7BgK,EAAA,IAAGL,OAAHlG,MAAAuG,EAAazO,SAASmE,KAAKqK,UACxB9L,SAAQ,SAAAmT,GAAI,OAAI9N,EAAaC,IAAI6N,EAAM,YAAa,MV5N5C,kBU+NbmE,EAAQzT,GAAG8F,aAAa,gBAAiB,SAErC4N,EAAQ7B,SACV6B,EAAQ7B,QAAQa,UAGlBkB,EAAa5O,UAAUC,OApVL,QAqVlBwO,EAAQzT,GAAGgF,UAAUC,OArVH,QAsVlBzD,EAAasB,QAAQkM,EA/VT,qBA+V+BzB,SAIxC8E,qBAAP,SAA4BzY,GAC1B,OAAOO,EAAuBP,IAAYA,EAAQsD,cAG7C2W,sBAAP,SAA6B1T,GAQ3B,KAAI,kBAAkBtD,KAAKsD,EAAMkB,OAAOyL,SAxX1B,UAyXZ3M,EAAM7B,KA1XO,WA0Xe6B,EAAM7B,MAtXjB,cAuXf6B,EAAM7B,KAxXO,YAwXmB6B,EAAM7B,KACtC6B,EAAMkB,OAAOyD,QAjWC,oBAkWfkM,GAAenU,KAAKsD,EAAM7B,QAI7B6B,EAAM4D,iBACN5D,EAAMyS,mBAEFtR,KAAK4Q,WAAY5Q,KAAK0D,UAAUE,SAlXZ,aAkXxB,CAIA,IAAM8J,EAAS4C,EAASS,qBAAqB/Q,MACvC6Q,EAAW7Q,KAAK0D,UAAUE,SAtXZ,QAwXpB,GA3Ye,WA2YX/E,EAAM7B,IAIR,OAHegD,KAAKoG,QAnXG,+BAmX6BpG,KAAOmG,EAAegB,KAAKnH,KAnXxD,+BAmXoF,IACpGmR,aACPb,EAASQ,aAIX,GAAKD,GAjZS,UAiZGhS,EAAM7B,IAAvB,CAKA,IAAMwV,EAAQrM,EAAeE,KA1XF,8DA0X+BqH,GAAQrI,OAAO3J,GAEzE,GAAK8W,EAAM5T,OAAX,CAIA,IAAIqL,EAAQuI,EAAM/G,QAAQ5M,EAAMkB,QA1Zf,YA6ZblB,EAAM7B,KAAwBiN,EAAQ,GACxCA,IA7ZiB,cAiafpL,EAAM7B,KAA0BiN,EAAQuI,EAAM5T,OAAS,GACzDqL,IAMFuI,EAFAvI,GAAmB,IAAXA,EAAe,EAAIA,GAEdkH,cAzBXb,EAASQ,uDAtUX,OAAOrJ,uCAIP,OAAOO,oCAIP,MAzFa,oBAkEXsI,CAAiB5N,GAwXvBxC,EAAaQ,GAAGvI,SAvaY,+BAUC,8BA6Z2CmY,GAASiC,uBACjFrS,EAAaQ,GAAGvI,SAxaY,+BAYN,iBA4Z2CmY,GAASiC,uBAC1ErS,EAAaQ,GAAGvI,SA1aU,6BA0asBmY,GAASQ,YACzD5Q,EAAaQ,GAAGvI,SAzaU,6BAyasBmY,GAASQ,YACzD5Q,EAAaQ,GAAGvI,SA5aU,6BAWG,+BAiayC,SAAU0G,GAC9EA,EAAM4D,iBACN5D,EAAMyS,kBACNhB,GAAS4B,kBAAkBlS,KAAM,aAEnCE,EAAaQ,GAAGvI,SAjbU,6BAYE,kBAqayC,SAAAkT,GAAC,OAAIA,EAAEiG,qBAS5E9U,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAGoD,IAChCrB,EAAE/B,GAAGoD,IAAQsN,GAASvM,gBACtBpC,EAAE/B,GAAGoD,IAAMoB,YAAckM,GACzB3O,EAAE/B,GAAGoD,IAAMqB,WAAa,WAEtB,OADA1C,EAAE/B,GAAGoD,IAAQmB,EACNmM,GAASvM,qBCtdtB,IAMM0D,GAAU,CACdgL,UAAU,EACV9K,UAAU,EACVwJ,OAAO,GAGHnJ,GAAc,CAClByK,SAAU,mBACV9K,SAAU,UACVwJ,MAAO,WAoCHuB,GAAAA,SAAAA,GACJ,SAAAA,EAAYpa,EAASmC,GAAQ,IAAAgJ,EAAA,OAC3BA,EAAA4E,EAAAlN,KAAA6E,KAAM1H,IAAN0H,MAEK8I,QAAUrF,EAAKsF,WAAWtO,GAC/BgJ,EAAKkP,QAAUxM,EAAeO,QAlBV,gBAkBmCpO,GACvDmL,EAAKmP,UAAY,KACjBnP,EAAKoP,UAAW,EAChBpP,EAAKqP,oBAAqB,EAC1BrP,EAAKsP,sBAAuB,EAC5BtP,EAAKmK,kBAAmB,EACxBnK,EAAKuP,gBAAkB,EAVIvP,oCAyB7Bc,OAAA,SAAO0H,GACL,OAAOjM,KAAK6S,SAAW7S,KAAKuO,OAASvO,KAAKwO,KAAKvC,MAGjDuC,KAAA,SAAKvC,GAAe,IAAA/B,EAAAlK,KAClB,IAAIA,KAAK6S,WAAY7S,KAAK4N,iBAA1B,CAII5N,KAAK2C,SAASe,UAAUE,SApDR,UAqDlB5D,KAAK4N,kBAAmB,GAG1B,IAAMqF,EAAY/S,EAAasB,QAAQxB,KAAK2C,SArEhC,gBAqEsD,CAChEsJ,cAAAA,IAGEjM,KAAK6S,UAAYI,EAAUlR,mBAI/B/B,KAAK6S,UAAW,EAEhB7S,KAAKkT,kBACLlT,KAAKmT,gBAELnT,KAAKoT,gBAELpT,KAAKqT,kBACLrT,KAAKsT,kBAELpT,EAAaQ,GAAGV,KAAK2C,SAnFA,yBAgBK,6BAmEiD,SAAA9D,GAAK,OAAIqL,EAAKqE,KAAK1P,MAE9FqB,EAAaQ,GAAGV,KAAK2S,QAlFI,8BAkF8B,WACrDzS,EAAaS,IAAIuJ,EAAKvH,SApFD,4BAoFkC,SAAA9D,GACjDA,EAAMkB,SAAWmK,EAAKvH,WACxBuH,EAAK6I,sBAAuB,SAKlC/S,KAAKuT,eAAc,WAAA,OAAMrJ,EAAKsJ,aAAavH,WAG7CsC,KAAA,SAAK1P,GAAO,IAAA6L,EAAA1K,KAKV,IAJInB,GACFA,EAAM4D,iBAGHzC,KAAK6S,WAAY7S,KAAK4N,oBAIT1N,EAAasB,QAAQxB,KAAK2C,SAhHhC,iBAkHEZ,iBAAd,CAIA/B,KAAK6S,UAAW,EAChB,IAAMY,EAAazT,KAAK2C,SAASe,UAAUE,SAvGvB,QAuHpB,GAdI6P,IACFzT,KAAK4N,kBAAmB,GAG1B5N,KAAKqT,kBACLrT,KAAKsT,kBAELpT,EAAaC,IAAIhI,SA3HF,oBA6Hf6H,KAAK2C,SAASe,UAAUC,OAjHJ,QAmHpBzD,EAAaC,IAAIH,KAAK2C,SA7HD,0BA8HrBzC,EAAaC,IAAIH,KAAK2S,QA3HG,8BA6HrBc,EAAY,CACd,IAAMva,EAAqBJ,EAAiCkH,KAAK2C,UAEjEzC,EAAaS,IAAIX,KAAK2C,SXlLL,iBWkL+B,SAAA9D,GAAK,OAAI6L,EAAKgJ,WAAW7U,MACzE9E,EAAqBiG,KAAK2C,SAAUzJ,QAEpC8G,KAAK0T,iBAIT5Q,QAAA,WACE,CAAC9J,OAAQgH,KAAK2C,SAAU3C,KAAK2S,SAC1B9X,SAAQ,SAAA8Y,GAAW,OAAIzT,EAAaC,IAAIwT,EAnKhC,gBAqKXtL,EAAA5B,UAAM3D,QAAN3H,KAAA6E,MAOAE,EAAaC,IAAIhI,SAvJF,oBAyJf6H,KAAK8I,QAAU,KACf9I,KAAK2S,QAAU,KACf3S,KAAK4S,UAAY,KACjB5S,KAAK6S,SAAW,KAChB7S,KAAK8S,mBAAqB,KAC1B9S,KAAK+S,qBAAuB,KAC5B/S,KAAK4N,iBAAmB,KACxB5N,KAAKgT,gBAAkB,QAGzBY,aAAA,WACE5T,KAAKoT,mBAKPrK,WAAA,SAAWtO,GAMT,OALAA,EAAM6P,EAAA,GACD7C,GACAhN,GAELF,EArMS,QAqMaE,EAAQuN,IACvBvN,KAGT+Y,aAAA,SAAavH,GAAe,IAAApB,EAAA7K,KACpByT,EAAazT,KAAK2C,SAASe,UAAUE,SAxKvB,QAyKdiQ,EAAY1N,EAAeO,QApKT,cAoKsC1G,KAAK2S,SAE9D3S,KAAK2C,SAAS/G,YAAcoE,KAAK2C,SAAS/G,WAAW9B,WAAakN,KAAKC,cAE1E9O,SAASmE,KAAKwX,YAAY9T,KAAK2C,UAGjC3C,KAAK2C,SAAShH,MAAMI,QAAU,QAC9BiE,KAAK2C,SAASsC,gBAAgB,eAC9BjF,KAAK2C,SAAS6B,aAAa,cAAc,GACzCxE,KAAK2C,SAAS6B,aAAa,OAAQ,UACnCxE,KAAK2C,SAASkD,UAAY,EAEtBgO,IACFA,EAAUhO,UAAY,GAGpB4N,GACFvX,EAAO8D,KAAK2C,UAGd3C,KAAK2C,SAASe,UAAU4H,IA7LJ,QA+LhBtL,KAAK8I,QAAQqI,OACfnR,KAAK+T,gBAGP,IAAMC,EAAqB,WACrBnJ,EAAK/B,QAAQqI,OACftG,EAAKlI,SAASwO,QAGhBtG,EAAK+C,kBAAmB,EACxB1N,EAAasB,QAAQqJ,EAAKlI,SAtNf,iBAsNsC,CAC/CsJ,cAAAA,KAIJ,GAAIwH,EAAY,CACd,IAAMva,EAAqBJ,EAAiCkH,KAAK2S,SAEjEzS,EAAaS,IAAIX,KAAK2S,QX1QL,gBW0Q8BqB,GAC/Cja,EAAqBiG,KAAK2S,QAASzZ,QAEnC8a,OAIJD,cAAA,WAAgB,IAAAjH,EAAA9M,KACdE,EAAaC,IAAIhI,SArOF,oBAsOf+H,EAAaQ,GAAGvI,SAtOD,oBAsO0B,SAAA0G,GACnC1G,WAAa0G,EAAMkB,QACnB+M,EAAKnK,WAAa9D,EAAMkB,QACvB+M,EAAKnK,SAASiB,SAAS/E,EAAMkB,SAChC+M,EAAKnK,SAASwO,cAKpBkC,gBAAA,WAAkB,IAAAY,EAAAjU,KACZA,KAAK6S,SACP3S,EAAaQ,GAAGV,KAAK2C,SA9OA,4BA8OiC,SAAA9D,GAChDoV,EAAKnL,QAAQnB,UArQN,WAqQkB9I,EAAM7B,KACjC6B,EAAM4D,iBACNwR,EAAK1F,QACK0F,EAAKnL,QAAQnB,UAxQd,WAwQ0B9I,EAAM7B,KACzCiX,EAAKC,gCAIThU,EAAaC,IAAIH,KAAK2C,SAvPD,+BA2PzB2Q,gBAAA,WAAkB,IAAAa,EAAAnU,KACZA,KAAK6S,SACP3S,EAAaQ,GAAG1H,OA/PJ,mBA+P0B,WAAA,OAAMmb,EAAKf,mBAEjDlT,EAAaC,IAAInH,OAjQL,sBAqQhB0a,WAAA,WAAa,IAAAU,EAAApU,KACXA,KAAK2C,SAAShH,MAAMI,QAAU,OAC9BiE,KAAK2C,SAAS6B,aAAa,eAAe,GAC1CxE,KAAK2C,SAASsC,gBAAgB,cAC9BjF,KAAK2C,SAASsC,gBAAgB,QAC9BjF,KAAK4N,kBAAmB,EACxB5N,KAAKuT,eAAc,WACjBpb,SAASmE,KAAKoH,UAAUC,OAnQN,cAoQlByQ,EAAKC,oBACLD,EAAKE,kBACLpU,EAAasB,QAAQ4S,EAAKzR,SAnRd,yBAuRhB4R,gBAAA,WACEvU,KAAK4S,UAAUhX,WAAWkI,YAAY9D,KAAK4S,WAC3C5S,KAAK4S,UAAY,QAGnBW,cAAA,SAAc9W,GAAU,IAAA+X,EAAAxU,KAChByU,EAAUzU,KAAK2C,SAASe,UAAUE,SA/QpB,QAAA,OAiRlB,GAEF,GAAI5D,KAAK6S,UAAY7S,KAAK8I,QAAQ2J,SAAU,CAiC1C,GAhCAzS,KAAK4S,UAAYza,SAASuc,cAAc,OACxC1U,KAAK4S,UAAU+B,UAvRO,iBAyRlBF,GACFzU,KAAK4S,UAAUlP,UAAU4H,IAAImJ,GAG/Btc,SAASmE,KAAKwX,YAAY9T,KAAK4S,WAE/B1S,EAAaQ,GAAGV,KAAK2C,SAtSF,0BAsSiC,SAAA9D,GAC9C2V,EAAKzB,qBACPyB,EAAKzB,sBAAuB,EAI1BlU,EAAMkB,SAAWlB,EAAM+V,gBAIG,WAA1BJ,EAAK1L,QAAQ2J,SACf+B,EAAKN,6BAELM,EAAKjG,WAILkG,GACFvY,EAAO8D,KAAK4S,WAGd5S,KAAK4S,UAAUlP,UAAU4H,IAjTP,SAmTbmJ,EAEH,YADAhY,IAIF,IAAMoY,EAA6B/b,EAAiCkH,KAAK4S,WAEzE1S,EAAaS,IAAIX,KAAK4S,UXnXL,gBWmXgCnW,GACjD1C,EAAqBiG,KAAK4S,UAAWiC,QAChC,IAAK7U,KAAK6S,UAAY7S,KAAK4S,UAAW,CAC3C5S,KAAK4S,UAAUlP,UAAUC,OA7TP,QA+TlB,IAAMmR,EAAiB,WACrBN,EAAKD,kBACL9X,KAGF,GAAIuD,KAAK2C,SAASe,UAAUE,SArUV,QAqUqC,CACrD,IAAMiR,EAA6B/b,EAAiCkH,KAAK4S,WACzE1S,EAAaS,IAAIX,KAAK4S,UX/XP,gBW+XkCkC,GACjD/a,EAAqBiG,KAAK4S,UAAWiC,QAErCC,SAGFrY,OAIJyX,2BAAA,WAA6B,IAAAa,EAAA/U,KAE3B,IADkBE,EAAasB,QAAQxB,KAAK2C,SAjWtB,0BAkWRZ,iBAAd,CAIA,IAAMiT,EAAqBhV,KAAK2C,SAASsS,aAAe9c,SAASyE,gBAAgBsY,aAE5EF,IACHhV,KAAK2C,SAAShH,MAAMwZ,UAAY,UAGlCnV,KAAK2C,SAASe,UAAU4H,IA3VF,gBA4VtB,IAAM8J,EAA0Btc,EAAiCkH,KAAK2S,SACtEzS,EAAaC,IAAIH,KAAK2C,SXvZH,iBWwZnBzC,EAAaS,IAAIX,KAAK2C,SXxZH,iBWwZ6B,WAC9CoS,EAAKpS,SAASe,UAAUC,OA/VJ,gBAgWfqR,IACH9U,EAAaS,IAAIoU,EAAKpS,SX3ZP,iBW2ZiC,WAC9CoS,EAAKpS,SAAShH,MAAMwZ,UAAY,MAElCpb,EAAqBgb,EAAKpS,SAAUyS,OAGxCrb,EAAqBiG,KAAK2C,SAAUyS,GACpCpV,KAAK2C,SAASwO,YAOhBiC,cAAA,WACE,IAAM4B,EACJhV,KAAK2C,SAASsS,aAAe9c,SAASyE,gBAAgBsY,eAElDlV,KAAK8S,oBAAsBkC,IAAuBrY,GAAWqD,KAAK8S,qBAAuBkC,GAAsBrY,KACnHqD,KAAK2C,SAAShH,MAAM0Z,YAAiBrV,KAAKgT,gBAA1C,OAGGhT,KAAK8S,qBAAuBkC,IAAuBrY,IAAYqD,KAAK8S,oBAAsBkC,GAAsBrY,KACnHqD,KAAK2C,SAAShH,MAAM2Z,aAAkBtV,KAAKgT,gBAA3C,SAIJqB,kBAAA,WACErU,KAAK2C,SAAShH,MAAM0Z,YAAc,GAClCrV,KAAK2C,SAAShH,MAAM2Z,aAAe,MAGrCpC,gBAAA,WACE,IAAMxN,EAAOvN,SAASmE,KAAKqJ,wBAC3B3F,KAAK8S,mBAAqB9a,KAAKud,MAAM7P,EAAKI,KAAOJ,EAAK8P,OAASxc,OAAOyc,WACtEzV,KAAKgT,gBAAkBhT,KAAK0V,wBAG9BvC,cAAA,WAAgB,IAAAwC,EAAA3V,KACd,GAAIA,KAAK8S,mBAAoB,CAK3B3M,EAAeE,KAvYU,qDAwYtBxL,SAAQ,SAAAvC,GACP,IAAMsd,EAAgBtd,EAAQqD,MAAM2Z,aAC9BO,EAAoB7c,OAAOC,iBAAiBX,GAAS,iBAC3DwM,EAAYC,iBAAiBzM,EAAS,gBAAiBsd,GACvDtd,EAAQqD,MAAM2Z,aAAkBjc,OAAOC,WAAWuc,GAAqBF,EAAK3C,gBAA5E,QAIJ7M,EAAeE,KA/YW,eAgZvBxL,SAAQ,SAAAvC,GACP,IAAMwd,EAAexd,EAAQqD,MAAMoa,YAC7BC,EAAmBhd,OAAOC,iBAAiBX,GAAS,gBAC1DwM,EAAYC,iBAAiBzM,EAAS,eAAgBwd,GACtDxd,EAAQqD,MAAMoa,YAAiB1c,OAAOC,WAAW0c,GAAoBL,EAAK3C,gBAA1E,QAIJ,IAAM4C,EAAgBzd,SAASmE,KAAKX,MAAM2Z,aACpCO,EAAoB7c,OAAOC,iBAAiBd,SAASmE,MAAM,iBAEjEwI,EAAYC,iBAAiB5M,SAASmE,KAAM,gBAAiBsZ,GAC7Dzd,SAASmE,KAAKX,MAAM2Z,aAAkBjc,OAAOC,WAAWuc,GAAqB7V,KAAKgT,gBAAlF,KAGF7a,SAASmE,KAAKoH,UAAU4H,IAzaJ,iBA4atBgJ,gBAAA,WAEEnO,EAAeE,KAraY,qDAsaxBxL,SAAQ,SAAAvC,GACP,IAAM2d,EAAUnR,EAAYU,iBAAiBlN,EAAS,sBAC/B,IAAZ2d,IACTnR,EAAYE,oBAAoB1M,EAAS,iBACzCA,EAAQqD,MAAM2Z,aAAeW,MAKnC9P,EAAeE,KA9aa,eA+azBxL,SAAQ,SAAAvC,GACP,IAAM4d,EAASpR,EAAYU,iBAAiBlN,EAAS,qBAC/B,IAAX4d,IACTpR,EAAYE,oBAAoB1M,EAAS,gBACzCA,EAAQqD,MAAMoa,YAAcG,MAKlC,IAAMD,EAAUnR,EAAYU,iBAAiBrN,SAASmE,KAAM,sBACrC,IAAZ2Z,EACT9d,SAASmE,KAAKX,MAAM2Z,aAAe,IAEnCxQ,EAAYE,oBAAoB7M,SAASmE,KAAM,iBAC/CnE,SAASmE,KAAKX,MAAM2Z,aAAeW,MAIvCP,mBAAA,WACE,IAAMS,EAAYhe,SAASuc,cAAc,OACzCyB,EAAUxB,UA/cwB,0BAgdlCxc,SAASmE,KAAKwX,YAAYqC,GAC1B,IAAMC,EAAiBD,EAAUxQ,wBAAwB0Q,MAAQF,EAAUG,YAE3E,OADAne,SAASmE,KAAKwH,YAAYqS,GACnBC,KAKFrS,gBAAP,SAAuBtJ,EAAQwR,GAC7B,OAAOjM,KAAKgE,MAAK,WACf,IAAI/G,EAAOK,EAAa0C,KAxfb,YAyfL8I,EAAOwB,EAAA,GACR7C,GACA3C,EAAYI,kBAAkBlF,MACX,iBAAXvF,GAAuBA,EAASA,EAAS,IAOtD,GAJKwC,IACHA,EAAO,IAAIyV,EAAM1S,KAAM8I,IAGH,iBAAXrO,EAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAI4S,UAAJ,oBAAkC5S,EAAlC,KAGRwC,EAAKxC,GAAQwR,gDArcjB,OAAOxE,oCAIP,MAvEa,iBAkDXiL,CAAchQ,GAkepBxC,EAAaQ,GAAGvI,SAxfU,0BAWG,4BA6eyC,SAAU0G,GAAO,IAAA0X,EAAAvW,KAC/ED,EAASlH,EAAuBmH,MAEjB,MAAjBA,KAAKwL,SAAoC,SAAjBxL,KAAKwL,SAC/B3M,EAAM4D,iBAGRvC,EAAaS,IAAIZ,EAvgBH,iBAugBuB,SAAAkT,GAC/BA,EAAUlR,kBAKd7B,EAAaS,IAAIZ,EA9gBH,mBA8gByB,WACjCrE,EAAU6a,IACZA,EAAKpF,cAKX,IAAIlU,EAAOK,EAAayC,EAxiBT,YAyiBf,IAAK9C,EAAM,CACT,IAAMxC,EAAM6P,EAAA,GACPxF,EAAYI,kBAAkBnF,GAC9B+E,EAAYI,kBAAkBlF,OAGnC/C,EAAO,IAAIyV,GAAM3S,EAAQtF,GAG3BwC,EAAKuR,KAAKxO,SAUZxD,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAF,MAC3B+B,EAAE/B,GAAF,MAAa8S,GAAM3O,gBACnBpC,EAAE/B,GAAF,MAAWwE,YAAcsO,GACzB/Q,EAAE/B,GAAF,MAAWyE,WAAa,WAEtB,OADA1C,EAAE/B,GAAF,MAAauE,EACNuO,GAAM3O,qBC9lBnB,IAAMyS,GAAW,IAAIvY,IAAI,CACvB,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eAUIwY,GAAmB,8DAOnBC,GAAmB,qIAyBZC,GAAmB,CAE9BC,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAzCP,kBA0C7BC,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BC,KAAM,GACNC,EAAG,GACHC,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJjZ,EAAG,GACHkZ,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,IAGC,SAASC,GAAaC,EAAYC,EAAWC,GAAY,IAAAtS,EAC9D,IAAKoS,EAAW9Z,OACd,OAAO8Z,EAGT,GAAIE,GAAoC,mBAAfA,EACvB,OAAOA,EAAWF,GAQpB,IALA,IACMG,GADY,IAAI7f,OAAO8f,WACKC,gBAAgBL,EAAY,aACxDM,EAAgBre,OAAOC,KAAK+d,GAC5BM,GAAW3S,EAAA,IAAGC,OAAHlG,MAAAiG,EAAauS,EAAgBvc,KAAKwD,iBAAiB,MAZNoZ,EAAA,SAcrDxa,EAAOC,GAd8C,IAAAiI,EAetDuS,EAAKF,EAASva,GACd0a,EAASD,EAAGE,SAAShe,cAE3B,IAAK2d,EAAc5X,SAASgY,GAG1B,OAFAD,EAAGvd,WAAWkI,YAAYqV,GAE1B,WAGF,IAAMG,GAAgB1S,EAAA,IAAGL,OAAHlG,MAAAuG,EAAauS,EAAGhU,YAChCoU,EAAoB,GAAGhT,OAAOoS,EAAU,MAAQ,GAAIA,EAAUS,IAAW,IAE/EE,EAAcze,SAAQ,SAAA2e,IApFD,SAACA,EAAMC,GAC9B,IAAMC,EAAWF,EAAKH,SAAShe,cAE/B,GAAIoe,EAAqBrY,SAASsY,GAChC,OAAIlD,GAASlX,IAAIoa,IACRjZ,QAAQ+Y,EAAKG,UAAUve,MAAMqb,KAAqB+C,EAAKG,UAAUve,MAAMsb,KASlF,IAHA,IAAMkD,EAASH,EAAqBpU,QAAO,SAAAwU,GAAS,OAAIA,aAAqBve,UAGpEoD,EAAI,EAAGC,EAAMib,EAAOhb,OAAQF,EAAIC,EAAKD,IAC5C,GAAIgb,EAASte,MAAMwe,EAAOlb,IACxB,OAAO,EAIX,OAAO,GAiEEob,CAAiBN,EAAMD,IAC1BJ,EAAGlU,gBAAgBuU,EAAKH,cAfrB3a,EAAI,EAAGC,EAAMsa,EAASra,OAAQF,EAAIC,EAAKD,IAAKwa,EAA5Cxa,GAoBT,OAAOma,EAAgBvc,KAAKyd,UCvF9B,IAAM/W,GAAO,UAIPgX,GAAqB,IAAI1e,OAAJ,wBAAyC,KAC9D2e,GAAwB,IAAIhc,IAAI,CAAC,WAAY,YAAa,eAE1D+J,GAAc,CAClBkS,UAAW,UACXC,SAAU,SACVC,MAAO,4BACP5Y,QAAS,SACT6Y,MAAO,kBACPC,KAAM,UACN/hB,SAAU,mBACVoZ,UAAW,oBACXhD,UAAW,2BACX4L,mBAAoB,eACpBpK,SAAU,mBACVqK,YAAa,oBACbC,SAAU,UACV7B,WAAY,kBACZD,UAAW,SACXtI,aAAc,iBAGVqK,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAOle,EAAQ,OAAS,QACxBme,OAAQ,SACRC,KAAMpe,EAAQ,QAAU,QAGpB8K,GAAU,CACdyS,WAAW,EACXC,SAAU,+GAIV3Y,QAAS,cACT4Y,MAAO,GACPC,MAAO,EACPC,MAAM,EACN/hB,UAAU,EACVoZ,UAAW,MACXhD,WAAW,EACX4L,mBAAoB,KACpBpK,SAAU,kBACVqK,YAAa,GACbC,UAAU,EACV7B,WAAY,KACZD,UAAWhC,GACXtG,aAAc,MAGV1W,GAAQ,CACZqhB,KAAI,kBACJC,OAAM,oBACNC,KAAI,kBACJC,MAAK,mBACLC,SAAQ,sBACRC,MAAK,mBACLC,QAAO,qBACPC,SAAQ,sBACRC,WAAU,wBACVC,WAAU,yBAuBNC,GAAAA,SAAAA,GACJ,SAAAA,EAAYpjB,EAASmC,GAAQ,IAAAgJ,EAC3B,QAAsB,IAAXuN,EACT,MAAM,IAAI3D,UAAU,+DAFK,OAK3B5J,EAAA4E,EAAAlN,KAAA6E,KAAM1H,IAAN0H,MAGK2b,YAAa,EAClBlY,EAAKmY,SAAW,EAChBnY,EAAKoY,YAAc,GACnBpY,EAAKqY,eAAiB,GACtBrY,EAAK8M,QAAU,KAGf9M,EAAKhJ,OAASgJ,EAAKsF,WAAWtO,GAC9BgJ,EAAKsY,IAAM,KAEXtY,EAAKuY,gBAlBsBvY,oCAiD7BwY,OAAA,WACEjc,KAAK2b,YAAa,KAGpBO,QAAA,WACElc,KAAK2b,YAAa,KAGpBQ,cAAA,WACEnc,KAAK2b,YAAc3b,KAAK2b,cAG1BpX,OAAA,SAAO1F,GACL,GAAKmB,KAAK2b,WAIV,GAAI9c,EAAO,CACT,IAAMud,EAAUpc,KAAK4C,YAAYC,SAC7BuP,EAAU9U,EAAauB,EAAMoB,eAAgBmc,GAE5ChK,IACHA,EAAU,IAAIpS,KAAK4C,YAAY/D,EAAMoB,eAAgBD,KAAKqc,sBAC1D/e,EAAauB,EAAMoB,eAAgBmc,EAAShK,IAG9CA,EAAQ0J,eAAeQ,OAASlK,EAAQ0J,eAAeQ,MAEnDlK,EAAQmK,uBACVnK,EAAQoK,OAAO,KAAMpK,GAErBA,EAAQqK,OAAO,KAAMrK,OAElB,CACL,GAAIpS,KAAK0c,gBAAgBhZ,UAAUE,SAtGjB,QAwGhB,YADA5D,KAAKyc,OAAO,KAAMzc,MAIpBA,KAAKwc,OAAO,KAAMxc,UAItB8C,QAAA,WACEqI,aAAanL,KAAK4b,UAElB1b,EAAaC,IAAIH,KAAK2C,SAAU3C,KAAK4C,YAAY4E,WACjDtH,EAAaC,IAAIH,KAAK2C,SAASa,QAAd,UAA+C,gBAAiBxD,KAAK2c,mBAElF3c,KAAK+b,KACP/b,KAAK+b,IAAIngB,WAAWkI,YAAY9D,KAAK+b,KAGvC/b,KAAK2b,WAAa,KAClB3b,KAAK4b,SAAW,KAChB5b,KAAK6b,YAAc,KACnB7b,KAAK8b,eAAiB,KAClB9b,KAAKuQ,SACPvQ,KAAKuQ,QAAQa,UAGfpR,KAAKuQ,QAAU,KACfvQ,KAAKvF,OAAS,KACduF,KAAK+b,IAAM,KACX1T,EAAA5B,UAAM3D,QAAN3H,KAAA6E,SAGFwO,KAAA,WAAO,IAAAtE,EAAAlK,KACL,GAAoC,SAAhCA,KAAK2C,SAAShH,MAAMI,QACtB,MAAM,IAAIP,MAAM,uCAGlB,GAAIwE,KAAK4c,iBAAmB5c,KAAK2b,WAAY,CAC3C,IAAM1I,EAAY/S,EAAasB,QAAQxB,KAAK2C,SAAU3C,KAAK4C,YAAYjJ,MAAMuhB,MACvE2B,Eb5GW,SAAjBC,EAAiBxkB,GACrB,IAAKH,SAASyE,gBAAgBmgB,aAC5B,OAAO,KAIT,GAAmC,mBAAxBzkB,EAAQ0kB,YAA4B,CAC7C,IAAMC,EAAO3kB,EAAQ0kB,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,KAG7C,OAAI3kB,aAAmB4kB,WACd5kB,EAIJA,EAAQsD,WAINkhB,EAAexkB,EAAQsD,YAHrB,Ka2FckhB,CAAe9c,KAAK2C,UACjCwa,EAA4B,OAAfN,EACjB7c,KAAK2C,SAASya,cAAcxgB,gBAAgBgH,SAAS5D,KAAK2C,UAC1Dka,EAAWjZ,SAAS5D,KAAK2C,UAE3B,GAAIsQ,EAAUlR,mBAAqBob,EACjC,OAGF,IAAMpB,EAAM/b,KAAK0c,gBACXW,EAAQvlB,EAAOkI,KAAK4C,YAAYI,MAEtC+Y,EAAIvX,aAAa,KAAM6Y,GACvBrd,KAAK2C,SAAS6B,aAAa,mBAAoB6Y,GAE/Crd,KAAKsd,aAEDtd,KAAKvF,OAAOyf,WACd6B,EAAIrY,UAAU4H,IAlKE,QAqKlB,IAAMqG,EAA6C,mBAA1B3R,KAAKvF,OAAOkX,UACnC3R,KAAKvF,OAAOkX,UAAUxW,KAAK6E,KAAM+b,EAAK/b,KAAK2C,UAC3C3C,KAAKvF,OAAOkX,UAER4L,EAAavd,KAAKwd,eAAe7L,GACvC3R,KAAKyd,oBAAoBF,GAEzB,IAAM5O,EAAY3O,KAAK0d,gBACvBpgB,EAAaye,EAAK/b,KAAK4C,YAAYC,SAAU7C,MAExCA,KAAK2C,SAASya,cAAcxgB,gBAAgBgH,SAAS5D,KAAK+b,MAC7DpN,EAAUmF,YAAYiI,GAGxB7b,EAAasB,QAAQxB,KAAK2C,SAAU3C,KAAK4C,YAAYjJ,MAAMyhB,UAE3Dpb,KAAKuQ,QAAUS,EAAAA,aAAoBhR,KAAK2C,SAAUoZ,EAAK/b,KAAKkR,iBAAiBqM,IAE7ExB,EAAIrY,UAAU4H,IArLI,QAuLlB,IACiBqS,EAQ+BrX,EAT1CkU,EAAiD,mBAA5Bxa,KAAKvF,OAAO+f,YAA6Bxa,KAAKvF,OAAO+f,cAAgBxa,KAAKvF,OAAO+f,YAC5G,GAAIA,GACFmD,EAAA5B,EAAIrY,WAAU4H,IAAdjL,MAAAsd,EAAqBnD,EAAYhhB,MAAM,MAOzC,GAAI,iBAAkBrB,SAASyE,iBAC7B0J,EAAA,IAAGC,OAAHlG,MAAAiG,EAAanO,SAASmE,KAAKqK,UAAU9L,SAAQ,SAAAvC,GAC3C4H,EAAaQ,GAAGpI,EAAS,abzIhB,kBa6Ib,IAAMslB,EAAW,WACf,IAAMC,EAAiB3T,EAAK2R,YAE5B3R,EAAK2R,YAAc,KACnB3b,EAAasB,QAAQ0I,EAAKvH,SAAUuH,EAAKtH,YAAYjJ,MAAMwhB,OAvM3C,QAyMZ0C,GACF3T,EAAKuS,OAAO,KAAMvS,IAItB,GAAIlK,KAAK+b,IAAIrY,UAAUE,SAnNL,QAmNgC,CAChD,IAAM1K,EAAqBJ,EAAiCkH,KAAK+b,KACjE7b,EAAaS,IAAIX,KAAK+b,IbvTP,gBauT4B6B,GAC3C7jB,EAAqBiG,KAAK+b,IAAK7iB,QAE/B0kB,QAKNrP,KAAA,WAAO,IAAA7D,EAAA1K,KACL,GAAKA,KAAKuQ,QAAV,CAIA,IAAMwL,EAAM/b,KAAK0c,gBACXkB,EAAW,WA/NI,SAgOflT,EAAKmR,aAAoCE,EAAIngB,YAC/CmgB,EAAIngB,WAAWkI,YAAYiY,GAG7BrR,EAAKoT,iBACLpT,EAAK/H,SAASsC,gBAAgB,oBAC9B/E,EAAasB,QAAQkJ,EAAK/H,SAAU+H,EAAK9H,YAAYjJ,MAAMshB,QAEvDvQ,EAAK6F,UACP7F,EAAK6F,QAAQa,UACb1G,EAAK6F,QAAU,OAKnB,IADkBrQ,EAAasB,QAAQxB,KAAK2C,SAAU3C,KAAK4C,YAAYjJ,MAAMqhB,MAC/DjZ,iBAAd,CAQgD,IAAA6E,EAAhD,GAJAmV,EAAIrY,UAAUC,OArPM,QAyPhB,iBAAkBxL,SAASyE,iBAC7BgK,EAAA,IAAGL,OAAHlG,MAAAuG,EAAazO,SAASmE,KAAKqK,UACxB9L,SAAQ,SAAAvC,GAAO,OAAI4H,EAAaC,IAAI7H,EAAS,YAAa2D,MAO/D,GAJA+D,KAAK8b,eAAL,OAAqC,EACrC9b,KAAK8b,eAAL,OAAqC,EACrC9b,KAAK8b,eAAL,OAAqC,EAEjC9b,KAAK+b,IAAIrY,UAAUE,SApQH,QAoQ8B,CAChD,IAAM1K,EAAqBJ,EAAiCijB,GAE5D7b,EAAaS,IAAIob,EbzWA,gBayWqB6B,GACtC7jB,EAAqBgiB,EAAK7iB,QAE1B0kB,IAGF5d,KAAK6b,YAAc,QAGrBxK,OAAA,WACuB,OAAjBrR,KAAKuQ,SACPvQ,KAAKuQ,QAAQc,YAMjBuL,cAAA,WACE,OAAOnc,QAAQT,KAAK+d,eAGtBrB,cAAA,WACE,GAAI1c,KAAK+b,IACP,OAAO/b,KAAK+b,IAGd,IAAMzjB,EAAUH,SAASuc,cAAc,OAIvC,OAHApc,EAAQyhB,UAAY/Z,KAAKvF,OAAO0f,SAEhCna,KAAK+b,IAAMzjB,EAAQqO,SAAS,GACrB3G,KAAK+b,OAGduB,WAAA,WACE,IAAMvB,EAAM/b,KAAK0c,gBACjB1c,KAAKge,kBAAkB7X,EAAeO,QAnSX,iBAmS2CqV,GAAM/b,KAAK+d,YACjFhC,EAAIrY,UAAUC,OA3SM,OAEA,WA4StBqa,kBAAA,SAAkB1lB,EAAS2lB,GACzB,GAAgB,OAAZ3lB,EAIJ,MAAuB,iBAAZ2lB,GAAwBrkB,EAAUqkB,IACvCA,EAAQ5O,SACV4O,EAAUA,EAAQ,SAIhBje,KAAKvF,OAAO6f,KACV2D,EAAQriB,aAAetD,IACzBA,EAAQyhB,UAAY,GACpBzhB,EAAQwb,YAAYmK,IAGtB3lB,EAAQ4lB,YAAcD,EAAQC,mBAM9Ble,KAAKvF,OAAO6f,MACVta,KAAKvF,OAAOggB,WACdwD,EAAUxF,GAAawF,EAASje,KAAKvF,OAAOke,UAAW3Y,KAAKvF,OAAOme,aAGrEtgB,EAAQyhB,UAAYkE,GAEpB3lB,EAAQ4lB,YAAcD,MAI1BF,SAAA,WACE,IAAI3D,EAAQpa,KAAK2C,SAASnK,aAAa,0BAQvC,OANK4hB,IACHA,EAAqC,mBAAtBpa,KAAKvF,OAAO2f,MACzBpa,KAAKvF,OAAO2f,MAAMjf,KAAK6E,KAAK2C,UAC5B3C,KAAKvF,OAAO2f,OAGTA,KAGT+D,iBAAA,SAAiBZ,GACf,MAAmB,UAAfA,EACK,MAGU,SAAfA,EACK,QAGFA,KAKTrM,iBAAA,SAAiBqM,GAAY,IAAA1S,EAAA7K,KACrBoe,EAAe,CACnBvM,KAAM,OACNC,QAAS,CACPC,aAAa,IAsCjB,OAlCI/R,KAAKvF,OAAO8f,qBACd6D,EAAatM,QAAQyI,mBAAqBva,KAAKvF,OAAO8f,oBAiCxDjQ,EAAA,GA9BwB,CACtBqH,UAAW4L,EACX3L,UAAW,CACTwM,EACA,CACEvM,KAAM,kBACNC,QAAS,CACPE,aAAchS,KAAKvF,OAAO0V,WAG9B,CACE0B,KAAM,QACNC,QAAS,CACPxZ,QAAO,IAAM0H,KAAK4C,YAAYI,KAAvB,WAGX,CACE6O,KAAM,WACNI,SAAS,EACToM,MAAO,aACPze,GAAI,SAAA3C,GAAI,OAAI4N,EAAKyT,6BAA6BrhB,MAGlDshB,cAAe,SAAAthB,GACTA,EAAK6U,QAAQH,YAAc1U,EAAK0U,WAClC9G,EAAKyT,6BAA6BrhB,KAOnC+C,KAAKvF,OAAO4V,iBAInBoN,oBAAA,SAAoBF,GAClBvd,KAAK0c,gBAAgBhZ,UAAU4H,IAAOkT,cAAgBxe,KAAKme,iBAAiBZ,OAG9EG,cAAA,WACE,OAA8B,IAA1B1d,KAAKvF,OAAOkU,UACPxW,SAASmE,KAGd1C,EAAUoG,KAAKvF,OAAOkU,WACjB3O,KAAKvF,OAAOkU,UAGdxI,EAAeO,QAAQ1G,KAAKvF,OAAOkU,cAG5C6O,eAAA,SAAe7L,GACb,OAAO+I,GAAc/I,EAAUlW,kBAGjCugB,cAAA,WAAgB,IAAAlP,EAAA9M,KACGA,KAAKvF,OAAO+G,QAAQhI,MAAM,KAElCqB,SAAQ,SAAA2G,GACf,GAAgB,UAAZA,EACFtB,EAAaQ,GAAGoM,EAAKnK,SAAUmK,EAAKlK,YAAYjJ,MAAM0hB,MAAOvO,EAAKrS,OAAOlC,UAAU,SAAAsG,GAAK,OAAIiO,EAAKvI,OAAO1F,WAEnG,GAzaU,WAyaN2C,EAA4B,CACrC,IAAMid,EA7aQ,UA6aEjd,EACdsL,EAAKlK,YAAYjJ,MAAM6hB,WACvB1O,EAAKlK,YAAYjJ,MAAM2hB,QACnBoD,EAhbQ,UAgbGld,EACfsL,EAAKlK,YAAYjJ,MAAM8hB,WACvB3O,EAAKlK,YAAYjJ,MAAM4hB,SAEzBrb,EAAaQ,GAAGoM,EAAKnK,SAAU8b,EAAS3R,EAAKrS,OAAOlC,UAAU,SAAAsG,GAAK,OAAIiO,EAAK0P,OAAO3d,MACnFqB,EAAaQ,GAAGoM,EAAKnK,SAAU+b,EAAU5R,EAAKrS,OAAOlC,UAAU,SAAAsG,GAAK,OAAIiO,EAAK2P,OAAO5d,UAIxFmB,KAAK2c,kBAAoB,WACnB7P,EAAKnK,UACPmK,EAAKyB,QAITrO,EAAaQ,GAAGV,KAAK2C,SAASa,QAAd,UAA+C,gBAAiBxD,KAAK2c,mBAEjF3c,KAAKvF,OAAOlC,SACdyH,KAAKvF,OAAL6P,EAAA,GACKtK,KAAKvF,OADV,CAEE+G,QAAS,SACTjJ,SAAU,KAGZyH,KAAK2e,eAITA,UAAA,WACE,IAAMvE,EAAQpa,KAAK2C,SAASnK,aAAa,SACnComB,SAA2B5e,KAAK2C,SAASnK,aAAa,2BAExD4hB,GAA+B,WAAtBwE,KACX5e,KAAK2C,SAAS6B,aAAa,yBAA0B4V,GAAS,KAC1DA,GAAUpa,KAAK2C,SAASnK,aAAa,eAAkBwH,KAAK2C,SAASub,aACvEle,KAAK2C,SAAS6B,aAAa,aAAc4V,GAG3Cpa,KAAK2C,SAAS6B,aAAa,QAAS,QAIxCgY,OAAA,SAAO3d,EAAOuT,GACZ,IAAMgK,EAAUpc,KAAK4C,YAAYC,UACjCuP,EAAUA,GAAW9U,EAAauB,EAAMoB,eAAgBmc,MAGtDhK,EAAU,IAAIpS,KAAK4C,YACjB/D,EAAMoB,eACND,KAAKqc,sBAEP/e,EAAauB,EAAMoB,eAAgBmc,EAAShK,IAG1CvT,IACFuT,EAAQ0J,eACS,YAAfjd,EAAMuB,KAveQ,QADA,UAyeZ,GAGFgS,EAAQsK,gBAAgBhZ,UAAUE,SAnflB,SAEC,SAif8CwO,EAAQyJ,YACzEzJ,EAAQyJ,YAlfW,QAsfrB1Q,aAAaiH,EAAQwJ,UAErBxJ,EAAQyJ,YAxfa,OA0fhBzJ,EAAQ3X,OAAO4f,OAAUjI,EAAQ3X,OAAO4f,MAAM7L,KAKnD4D,EAAQwJ,SAAWthB,YAAW,WA/fT,SAggBf8X,EAAQyJ,aACVzJ,EAAQ5D,SAET4D,EAAQ3X,OAAO4f,MAAM7L,MARtB4D,EAAQ5D,WAWZiO,OAAA,SAAO5d,EAAOuT,GACZ,IAAMgK,EAAUpc,KAAK4C,YAAYC,UACjCuP,EAAUA,GAAW9U,EAAauB,EAAMoB,eAAgBmc,MAGtDhK,EAAU,IAAIpS,KAAK4C,YACjB/D,EAAMoB,eACND,KAAKqc,sBAEP/e,EAAauB,EAAMoB,eAAgBmc,EAAShK,IAG1CvT,IACFuT,EAAQ0J,eACS,aAAfjd,EAAMuB,KA9gBQ,QADA,UAghBZ,GAGFgS,EAAQmK,yBAIZpR,aAAaiH,EAAQwJ,UAErBxJ,EAAQyJ,YA7hBY,MA+hBfzJ,EAAQ3X,OAAO4f,OAAUjI,EAAQ3X,OAAO4f,MAAM9L,KAKnD6D,EAAQwJ,SAAWthB,YAAW,WApiBV,QAqiBd8X,EAAQyJ,aACVzJ,EAAQ7D,SAET6D,EAAQ3X,OAAO4f,MAAM9L,MARtB6D,EAAQ7D,WAWZgO,qBAAA,WACE,IAAK,IAAM/a,KAAWxB,KAAK8b,eACzB,GAAI9b,KAAK8b,eAAeta,GACtB,OAAO,EAIX,OAAO,KAGTuH,WAAA,SAAWtO,GACT,IAAMokB,EAAiB/Z,EAAYI,kBAAkBlF,KAAK2C,UAuC1D,OArCAhI,OAAOC,KAAKikB,GAAgBhkB,SAAQ,SAAAikB,GAC9B7E,GAAsB3a,IAAIwf,WACrBD,EAAeC,MAItBrkB,GAAsC,iBAArBA,EAAOkU,WAA0BlU,EAAOkU,UAAUU,SACrE5U,EAAOkU,UAAYlU,EAAOkU,UAAU,IASV,iBAN5BlU,EAAM6P,EAAA,GACDtK,KAAK4C,YAAY6E,QACjBoX,EACmB,iBAAXpkB,GAAuBA,EAASA,EAAS,KAGpC4f,QAChB5f,EAAO4f,MAAQ,CACb7L,KAAM/T,EAAO4f,MACb9L,KAAM9T,EAAO4f,QAIW,iBAAjB5f,EAAO2f,QAChB3f,EAAO2f,MAAQ3f,EAAO2f,MAAMlf,YAGA,iBAAnBT,EAAOwjB,UAChBxjB,EAAOwjB,QAAUxjB,EAAOwjB,QAAQ/iB,YAGlCX,EAAgByI,GAAMvI,EAAQuF,KAAK4C,YAAYoF,aAE3CvN,EAAOggB,WACThgB,EAAO0f,SAAW1B,GAAahe,EAAO0f,SAAU1f,EAAOke,UAAWle,EAAOme,aAGpEne,KAGT4hB,mBAAA,WACE,IAAM5hB,EAAS,GAEf,GAAIuF,KAAKvF,OACP,IAAK,IAAMuC,KAAOgD,KAAKvF,OACjBuF,KAAK4C,YAAY6E,QAAQzK,KAASgD,KAAKvF,OAAOuC,KAChDvC,EAAOuC,GAAOgD,KAAKvF,OAAOuC,IAKhC,OAAOvC,KAGTqjB,eAAA,WACE,IAAM/B,EAAM/b,KAAK0c,gBACXqC,EAAWhD,EAAIvjB,aAAa,SAAS4C,MAAM4e,IAChC,OAAb+E,GAAqBA,EAASngB,OAAS,GACzCmgB,EAASC,KAAI,SAAAC,GAAK,OAAIA,EAAMvmB,UACzBmC,SAAQ,SAAAqkB,GAAM,OAAInD,EAAIrY,UAAUC,OAAOub,SAI9CZ,6BAAA,SAA6Ba,GAAY,IAC/BC,EAAUD,EAAVC,MAEHA,IAILpf,KAAK+b,IAAMqD,EAAMnG,SAASoG,OAC1Brf,KAAK8d,iBACL9d,KAAKyd,oBAAoBzd,KAAKwd,eAAe4B,EAAMzN,gBAK9C5N,gBAAP,SAAuBtJ,GACrB,OAAOuF,KAAKgE,MAAK,WACf,IAAI/G,EAAOK,EAAa0C,KAhtBb,cAitBL8I,EAA4B,iBAAXrO,GAAuBA,EAE9C,IAAKwC,IAAQ,eAAe1B,KAAKd,MAI5BwC,IACHA,EAAO,IAAIye,EAAQ1b,KAAM8I,IAGL,iBAAXrO,GAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAI4S,UAAJ,oBAAkC5S,EAAlC,KAGRwC,EAAKxC,kDA/mBT,OAAOgN,gCAIP,OAAOzE,oCAIP,MAzHa,2CA6Hb,OAAOrJ,qCAIP,MAhIW,kDAoIX,OAAOqO,SA7CL0T,CAAgBhZ,GAqpBtBlG,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAGoD,IAChCrB,EAAE/B,GAAGoD,IAAQ0Y,GAAQ3X,gBACrBpC,EAAE/B,GAAGoD,IAAMoB,YAAcsX,GACzB/Z,EAAE/B,GAAGoD,IAAMqB,WAAa,WAEtB,OADA1C,EAAE/B,GAAGoD,IAAQmB,EACNuX,GAAQ3X,qBC3wBrB,IAAMf,GAAO,UAIPgX,GAAqB,IAAI1e,OAAJ,wBAAyC,KAE9DmM,GAAO6C,EAAA,GACRoR,GAAQjU,QADA,CAEXkK,UAAW,QACXnQ,QAAS,QACTyc,QAAS,GACT9D,SAAU,gJAONnS,GAAWsC,EAAA,GACZoR,GAAQ1T,YADI,CAEfiW,QAAS,8BAGLtkB,GAAQ,CACZqhB,KAAI,kBACJC,OAAM,oBACNC,KAAI,kBACJC,MAAK,mBACLC,SAAQ,sBACRC,MAAK,mBACLC,QAAO,qBACPC,SAAQ,sBACRC,WAAU,wBACVC,WAAU,yBAeN6D,GAAAA,SAAAA,uFA6BJ1C,cAAA,WACE,OAAO5c,KAAK+d,YAAc/d,KAAKuf,iBAGjCjC,WAAA,WACE,IAAMvB,EAAM/b,KAAK0c,gBAGjB1c,KAAKge,kBAAkB7X,EAAeO,QA9CnB,kBA8C2CqV,GAAM/b,KAAK+d,YACzE,IAAIE,EAAUje,KAAKuf,cACI,mBAAZtB,IACTA,EAAUA,EAAQ9iB,KAAK6E,KAAK2C,WAG9B3C,KAAKge,kBAAkB7X,EAAeO,QAnDjB,gBAmD2CqV,GAAMkC,GAEtElC,EAAIrY,UAAUC,OAzDM,OACA,WA6DtB8Z,oBAAA,SAAoBF,GAClBvd,KAAK0c,gBAAgBhZ,UAAU4H,IAAOkT,cAAgBxe,KAAKme,iBAAiBZ,OAG9EgC,YAAA,WACE,OAAOvf,KAAK2C,SAASnK,aAAa,oBAAsBwH,KAAKvF,OAAOwjB,WAGtEH,eAAA,WACE,IAAM/B,EAAM/b,KAAK0c,gBACXqC,EAAWhD,EAAIvjB,aAAa,SAAS4C,MAAM4e,IAChC,OAAb+E,GAAqBA,EAASngB,OAAS,GACzCmgB,EAASC,KAAI,SAAAC,GAAK,OAAIA,EAAMvmB,UACzBmC,SAAQ,SAAAqkB,GAAM,OAAInD,EAAIrY,UAAUC,OAAOub,SAMvCnb,gBAAP,SAAuBtJ,GACrB,OAAOuF,KAAKgE,MAAK,WACf,IAAI/G,EAAOK,EAAa0C,KAtHb,cAuHL8I,EAA4B,iBAAXrO,EAAsBA,EAAS,KAEtD,IAAKwC,IAAQ,eAAe1B,KAAKd,MAI5BwC,IACHA,EAAO,IAAIqiB,EAAQtf,KAAM8I,GACzBxL,EAAa0C,KA/HJ,aA+HoB/C,IAGT,iBAAXxC,GAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAI4S,UAAJ,oBAAkC5S,EAAlC,KAGRwC,EAAKxC,kDApFT,OAAOgN,gCAIP,OAAOzE,oCAIP,MA3Da,2CA+Db,OAAOrJ,qCAIP,MAlEW,kDAsEX,OAAOqO,SAxBLsX,CAAgB5D,IAqGtBlf,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAGoD,IAChCrB,EAAE/B,GAAGoD,IAAQsc,GAAQvb,gBACrBpC,EAAE/B,GAAGoD,IAAMoB,YAAckb,GACzB3d,EAAE/B,GAAGoD,IAAMqB,WAAa,WAEtB,OADA1C,EAAE/B,GAAGoD,IAAQmB,EACNmb,GAAQvb,qBCrJrB,IAAMf,GAAO,YAKPyE,GAAU,CACdhC,OAAQ,GACR+Z,OAAQ,OACRzf,OAAQ,IAGJiI,GAAc,CAClBvC,OAAQ,SACR+Z,OAAQ,SACRzf,OAAQ,oBA2BJ0f,GAAAA,SAAAA,GACJ,SAAAA,EAAYnnB,EAASmC,GAAQ,IAAAgJ,EAAA,OAC3BA,EAAA4E,EAAAlN,KAAA6E,KAAM1H,IAAN0H,MACK0f,eAAqC,SAApBpnB,EAAQkT,QAAqBxS,OAASV,EAC5DmL,EAAKqF,QAAUrF,EAAKsF,WAAWtO,GAC/BgJ,EAAK0K,UAAe1K,EAAKqF,QAAQ/I,OAAb0D,eAA8CA,EAAKqF,QAAQ/I,OAA3D0D,sBAA6FA,EAAKqF,QAAQ/I,OAA1G0D,kBACpBA,EAAKkc,SAAW,GAChBlc,EAAKmc,SAAW,GAChBnc,EAAKoc,cAAgB,KACrBpc,EAAKqc,cAAgB,EAErB5f,EAAaQ,GAAG+C,EAAKic,eAlCP,uBAkCqC,SAAA7gB,GAAK,OAAI4E,EAAKsc,SAASlhB,MAE1E4E,EAAKuc,UACLvc,EAAKsc,WAbsBtc,oCA4B7Buc,QAAA,WAAU,IAAA9V,EAAAlK,KACFigB,EAAajgB,KAAK0f,iBAAmB1f,KAAK0f,eAAe1mB,OAvC7C,SACE,WA0CdknB,EAAuC,SAAxBlgB,KAAK8I,QAAQ0W,OAChCS,EACAjgB,KAAK8I,QAAQ0W,OAETW,EA9Cc,aA8CDD,EACjBlgB,KAAKogB,gBACL,EAEFpgB,KAAK2f,SAAW,GAChB3f,KAAK4f,SAAW,GAChB5f,KAAK8f,cAAgB9f,KAAKqgB,mBAEVla,EAAeE,KAAKrG,KAAKmO,WAEjC6Q,KAAI,SAAA1mB,GACV,IAAMgoB,EAAiB3nB,EAAuBL,GACxCyH,EAASugB,EAAiBna,EAAeO,QAAQ4Z,GAAkB,KAEzE,GAAIvgB,EAAQ,CACV,IAAMwgB,EAAYxgB,EAAO4F,wBACzB,GAAI4a,EAAUlK,OAASkK,EAAUC,OAC/B,MAAO,CACL1b,EAAYob,GAAcngB,GAAQ6F,IAAMua,EACxCG,GAKN,OAAO,QAENjb,QAAO,SAAAob,GAAI,OAAIA,KACfC,MAAK,SAAC7J,EAAGE,GAAJ,OAAUF,EAAE,GAAKE,EAAE,MACxBlc,SAAQ,SAAA4lB,GACPvW,EAAKyV,SAASzY,KAAKuZ,EAAK,IACxBvW,EAAK0V,SAAS1Y,KAAKuZ,EAAK,UAI9B3d,QAAA,WACEuF,EAAA5B,UAAM3D,QAAN3H,KAAA6E,MACAE,EAAaC,IAAIH,KAAK0f,eAjHX,iBAmHX1f,KAAK0f,eAAiB,KACtB1f,KAAK8I,QAAU,KACf9I,KAAKmO,UAAY,KACjBnO,KAAK2f,SAAW,KAChB3f,KAAK4f,SAAW,KAChB5f,KAAK6f,cAAgB,KACrB7f,KAAK8f,cAAgB,QAKvB/W,WAAA,SAAWtO,GAMT,GAA6B,iBAL7BA,EAAM6P,EAAA,GACD7C,GACmB,iBAAXhN,GAAuBA,EAASA,EAAS,KAGpCsF,QAAuBnG,EAAUa,EAAOsF,QAAS,CAAA,IAC3DlI,EAAO4C,EAAOsF,OAAdlI,GACDA,IACHA,EAAKC,EAAOkL,IACZvI,EAAOsF,OAAOlI,GAAKA,GAGrB4C,EAAOsF,OAAP,IAAoBlI,EAKtB,OAFA0C,EAAgByI,GAAMvI,EAAQuN,IAEvBvN,KAGT2lB,cAAA,WACE,OAAOpgB,KAAK0f,iBAAmB1mB,OAC7BgH,KAAK0f,eAAeiB,YACpB3gB,KAAK0f,eAAe7Z,aAGxBwa,iBAAA,WACE,OAAOrgB,KAAK0f,eAAezK,cAAgBjd,KAAK4oB,IAC9CzoB,SAASmE,KAAK2Y,aACd9c,SAASyE,gBAAgBqY,iBAI7B4L,iBAAA,WACE,OAAO7gB,KAAK0f,iBAAmB1mB,OAC7BA,OAAO8nB,YACP9gB,KAAK0f,eAAe/Z,wBAAwB6a,UAGhDT,SAAA,WACE,IAAMla,EAAY7F,KAAKogB,gBAAkBpgB,KAAK8I,QAAQrD,OAChDwP,EAAejV,KAAKqgB,mBACpBU,EAAY/gB,KAAK8I,QAAQrD,OAASwP,EAAejV,KAAK6gB,mBAM5D,GAJI7gB,KAAK8f,gBAAkB7K,GACzBjV,KAAKggB,UAGHna,GAAakb,EAAjB,CACE,IAAMhhB,EAASC,KAAK4f,SAAS5f,KAAK4f,SAAShhB,OAAS,GAEhDoB,KAAK6f,gBAAkB9f,GACzBC,KAAKghB,UAAUjhB,OAJnB,CAUA,GAAIC,KAAK6f,eAAiBha,EAAY7F,KAAK2f,SAAS,IAAM3f,KAAK2f,SAAS,GAAK,EAG3E,OAFA3f,KAAK6f,cAAgB,UACrB7f,KAAKihB,SAIP,IAAK,IAAIviB,EAAIsB,KAAK2f,SAAS/gB,OAAQF,KAAM,CAChBsB,KAAK6f,gBAAkB7f,KAAK4f,SAASlhB,IACxDmH,GAAa7F,KAAK2f,SAASjhB,UACM,IAAzBsB,KAAK2f,SAASjhB,EAAI,IAAsBmH,EAAY7F,KAAK2f,SAASjhB,EAAI,KAGhFsB,KAAKghB,UAAUhhB,KAAK4f,SAASlhB,SAKnCsiB,UAAA,SAAUjhB,GACRC,KAAK6f,cAAgB9f,EAErBC,KAAKihB,SAEL,IAAMC,EAAUlhB,KAAKmO,UAAU3U,MAAM,KAClCwlB,KAAI,SAAAzmB,GAAQ,OAAOA,EAAP,oBAAmCwH,EAAnC,MAA+CxH,EAA/C,UAAiEwH,EAAjE,QAETohB,EAAOhb,EAAeO,QAAQwa,EAAQE,KAAK,MAE7CD,EAAKzd,UAAUE,SAjMU,kBAkM3BuC,EAAeO,QAzLY,mBAyLsBya,EAAK3d,QA1LlC,cA2LjBE,UAAU4H,IAlMO,UAoMpB6V,EAAKzd,UAAU4H,IApMK,YAuMpB6V,EAAKzd,UAAU4H,IAvMK,UAyMpBnF,EAAeW,QAAQqa,EAtMG,qBAuMvBtmB,SAAQ,SAAAwmB,GAGPlb,EAAegB,KAAKka,EAAcC,+BAC/BzmB,SAAQ,SAAA4lB,GAAI,OAAIA,EAAK/c,UAAU4H,IA9MlB,aAiNhBnF,EAAegB,KAAKka,EA5MH,aA6MdxmB,SAAQ,SAAA0mB,GACPpb,EAAeQ,SAAS4a,EA/MX,aAgNV1mB,SAAQ,SAAA4lB,GAAI,OAAIA,EAAK/c,UAAU4H,IApNtB,oBAyNtBpL,EAAasB,QAAQxB,KAAK0f,eA9NV,wBA8N0C,CACxDzT,cAAelM,OAInBkhB,OAAA,WACE9a,EAAeE,KAAKrG,KAAKmO,WACtB9I,QAAO,SAAAmc,GAAI,OAAIA,EAAK9d,UAAUE,SAhOX,aAiOnB/I,SAAQ,SAAA2mB,GAAI,OAAIA,EAAK9d,UAAUC,OAjOZ,gBAsOjBI,gBAAP,SAAuBtJ,GACrB,OAAOuF,KAAKgE,MAAK,WACf,IAAI/G,EAAOK,EAAa0C,KA7Pb,gBAoQX,GAJK/C,IACHA,EAAO,IAAIwiB,EAAUzf,KAHW,iBAAXvF,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAI4S,UAAJ,oBAAkC5S,EAAlC,KAGRwC,EAAKxC,kDA7MT,OAAOgN,oCAIP,MAhEa,qBAwCXgY,CAAkB/c,GA6OxBxC,EAAaQ,GAAG1H,OAnQS,8BAmQoB,WAC3CmN,EAAeE,KA/PS,0BAgQrBxL,SAAQ,SAAA4mB,GAAG,OAAI,IAAIhC,GAAUgC,EAAK3c,EAAYI,kBAAkBuc,UAUrEjlB,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAGoD,IAChCrB,EAAE/B,GAAGoD,IAAQyc,GAAU1b,gBACvBpC,EAAE/B,GAAGoD,IAAMoB,YAAcqb,GACzB9d,EAAE/B,GAAGoD,IAAMqB,WAAa,WAEtB,OADA1C,EAAE/B,GAAGoD,IAAQmB,EACNsb,GAAU1b,qBC3SvB,IA+BM2d,GAAAA,SAAAA,uFASJlT,KAAA,WAAO,IAAA/K,EAAAzD,KACL,KAAKA,KAAK2C,SAAS/G,YACjBoE,KAAK2C,SAAS/G,WAAW9B,WAAakN,KAAKC,cAC3CjH,KAAK2C,SAASe,UAAUE,SA/BJ,WAgCpB5D,KAAK2C,SAASe,UAAUE,SA/BF,aA4BxB,CAOA,IAAIwD,EACErH,EAASlH,EAAuBmH,KAAK2C,UACrCgf,EAAc3hB,KAAK2C,SAASa,QAhCN,qBAkC5B,GAAIme,EAAa,CACf,IAAMC,EAAwC,OAAzBD,EAAYtI,UAA8C,OAAzBsI,EAAYtI,SAjC7C,wBADH,UAoClBjS,GADAA,EAAWjB,EAAeE,KAAKub,EAAcD,IACzBva,EAASxI,OAAS,GAGxC,IAAIijB,EAAY,KAYhB,GAVIza,IACFya,EAAY3hB,EAAasB,QAAQ4F,EAxDvB,cAwD6C,CACrD6E,cAAejM,KAAK2C,cAINzC,EAAasB,QAAQxB,KAAK2C,SA3DhC,cA2DsD,CAChEsJ,cAAe7E,IAGHrF,kBAAmC,OAAd8f,GAAsBA,EAAU9f,kBAAnE,CAIA/B,KAAKghB,UAAUhhB,KAAK2C,SAAUgf,GAE9B,IAAM/D,EAAW,WACf1d,EAAasB,QAAQ4F,EAvET,gBAuEiC,CAC3C6E,cAAexI,EAAKd,WAEtBzC,EAAasB,QAAQiC,EAAKd,SAxEf,eAwEsC,CAC/CsJ,cAAe7E,KAIfrH,EACFC,KAAKghB,UAAUjhB,EAAQA,EAAOnE,WAAYgiB,GAE1CA,SAMJoD,UAAA,SAAU1oB,EAASqW,EAAWlS,GAAU,IAAAyN,EAAAlK,KAKhC8hB,IAJiBnT,GAAqC,OAAvBA,EAAU0K,UAA4C,OAAvB1K,EAAU0K,SAE5ElT,EAAeQ,SAASgI,EA9EN,WA6ElBxI,EAAeE,KA5EM,wBA4EmBsI,IAGZ,GACxBS,EAAkB3S,GAAaqlB,GAAUA,EAAOpe,UAAUE,SAtF5C,QAwFdga,EAAW,WAAA,OAAM1T,EAAK6X,oBAAoBzpB,EAASwpB,EAAQrlB,IAEjE,GAAIqlB,GAAU1S,EAAiB,CAC7B,IAAMlW,EAAqBJ,EAAiCgpB,GAC5DA,EAAOpe,UAAUC,OA3FC,QA6FlBzD,EAAaS,IAAImhB,EhB9HA,gBgB8HwBlE,GACzC7jB,EAAqB+nB,EAAQ5oB,QAE7B0kB,OAIJmE,oBAAA,SAAoBzpB,EAASwpB,EAAQrlB,GACnC,GAAIqlB,EAAQ,CACVA,EAAOpe,UAAUC,OAzGG,UA2GpB,IAAMqe,EAAgB7b,EAAeO,QAhGJ,kCAgG4Cob,EAAOlmB,YAEhFomB,GACFA,EAActe,UAAUC,OA9GN,UAiHgB,QAAhCme,EAAOtpB,aAAa,SACtBspB,EAAOtd,aAAa,iBAAiB,IAIzClM,EAAQoL,UAAU4H,IAtHI,UAuHe,QAAjChT,EAAQE,aAAa,SACvBF,EAAQkM,aAAa,iBAAiB,GAGxCtI,EAAO5D,GAEHA,EAAQoL,UAAUE,SA3HF,SA4HlBtL,EAAQoL,UAAU4H,IA3HA,QA8HhBhT,EAAQsD,YAActD,EAAQsD,WAAW8H,UAAUE,SAlI1B,oBAmIHtL,EAAQkL,QA7HZ,cAgIlB2C,EAAeE,KA3HU,oBA4HtBxL,SAAQ,SAAAonB,GAAQ,OAAIA,EAASve,UAAU4H,IAtIxB,aAyIpBhT,EAAQkM,aAAa,iBAAiB,IAGpC/H,GACFA,OAMGsH,gBAAP,SAAuBtJ,GACrB,OAAOuF,KAAKgE,MAAK,WACf,IAAM/G,EAAOK,EAAa0C,KAhKf,WAgKkC,IAAI0hB,EAAI1hB,MAErD,GAAsB,iBAAXvF,EAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAI4S,UAAJ,oBAAkC5S,EAAlC,KAGRwC,EAAKxC,mDArIT,MAlCa,eA8BXinB,CAAYhf,GAqJlBxC,EAAaQ,GAAGvI,SA3KU,wBAYG,4EA+JyC,SAAU0G,GAC9EA,EAAM4D,kBAEOnF,EAAa0C,KAtLX,WAsL8B,IAAI0hB,GAAI1hB,OAChDwO,UAUPhS,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAF,IAC3B+B,EAAE/B,GAAF,IAAa8hB,GAAI3d,gBACjBpC,EAAE/B,GAAF,IAAWwE,YAAcsd,GACzB/f,EAAE/B,GAAF,IAAWyE,WAAa,WAEtB,OADA1C,EAAE/B,GAAF,IAAauE,EACNud,GAAI3d,qBC3MjB,IAeMiE,GAAc,CAClBkS,UAAW,UACXgI,SAAU,UACV7H,MAAO,UAGH5S,GAAU,CACdyS,WAAW,EACXgI,UAAU,EACV7H,MAAO,KAWH8H,GAAAA,SAAAA,GACJ,SAAAA,EAAY7pB,EAASmC,GAAQ,IAAAgJ,EAAA,OAC3BA,EAAA4E,EAAAlN,KAAA6E,KAAM1H,IAAN0H,MAEK8I,QAAUrF,EAAKsF,WAAWtO,GAC/BgJ,EAAKmY,SAAW,KAChBnY,EAAKuY,gBALsBvY,oCAwB7B+K,KAAA,WAAO,IAAAtE,EAAAlK,KAGL,IAFkBE,EAAasB,QAAQxB,KAAK2C,SAtDhC,iBAwDEZ,iBAAd,CAIA/B,KAAKoiB,gBAEDpiB,KAAK8I,QAAQoR,WACfla,KAAK2C,SAASe,UAAU4H,IA5DN,QA+DpB,IAAMsS,EAAW,WACf1T,EAAKvH,SAASe,UAAUC,OA7DH,WA8DrBuG,EAAKvH,SAASe,UAAU4H,IA/DN,QAiElBpL,EAAasB,QAAQ0I,EAAKvH,SArEf,kBAuEPuH,EAAKpB,QAAQoZ,WACfhY,EAAK0R,SAAWthB,YAAW,WACzB4P,EAAKqE,SACJrE,EAAKpB,QAAQuR,SAOpB,GAHAra,KAAK2C,SAASe,UAAUC,OA3EJ,QA4EpBzH,EAAO8D,KAAK2C,UACZ3C,KAAK2C,SAASe,UAAU4H,IA3ED,WA4EnBtL,KAAK8I,QAAQoR,UAAW,CAC1B,IAAMhhB,EAAqBJ,EAAiCkH,KAAK2C,UAEjEzC,EAAaS,IAAIX,KAAK2C,SjB9GL,gBiB8G+Bib,GAChD7jB,EAAqBiG,KAAK2C,SAAUzJ,QAEpC0kB,QAIJrP,KAAA,WAAO,IAAA7D,EAAA1K,KACL,GAAKA,KAAK2C,SAASe,UAAUE,SAxFT,UA4FF1D,EAAasB,QAAQxB,KAAK2C,SAnGhC,iBAqGEZ,iBAAd,CAIA,IAAM6b,EAAW,WACflT,EAAK/H,SAASe,UAAU4H,IApGN,QAqGlBpL,EAAasB,QAAQkJ,EAAK/H,SA1Gd,oBA8Gd,GADA3C,KAAK2C,SAASe,UAAUC,OAvGJ,QAwGhB3D,KAAK8I,QAAQoR,UAAW,CAC1B,IAAMhhB,EAAqBJ,EAAiCkH,KAAK2C,UAEjEzC,EAAaS,IAAIX,KAAK2C,SjBzIL,gBiByI+Bib,GAChD7jB,EAAqBiG,KAAK2C,SAAUzJ,QAEpC0kB,QAIJ9a,QAAA,WACE9C,KAAKoiB,gBAEDpiB,KAAK2C,SAASe,UAAUE,SArHR,SAsHlB5D,KAAK2C,SAASe,UAAUC,OAtHN,QAyHpBzD,EAAaC,IAAIH,KAAK2C,SAjID,0BAmIrB0F,EAAA5B,UAAM3D,QAAN3H,KAAA6E,MACAA,KAAK8I,QAAU,QAKjBC,WAAA,SAAWtO,GAST,OARAA,EAAM6P,EAAA,GACD7C,GACA3C,EAAYI,kBAAkBlF,KAAK2C,UAChB,iBAAXlI,GAAuBA,EAASA,EAAS,IAGtDF,EApJS,QAoJaE,EAAQuF,KAAK4C,YAAYoF,aAExCvN,KAGTuhB,cAAA,WAAgB,IAAAnR,EAAA7K,KACdE,EAAaQ,GAAGV,KAAK2C,SAtJA,yBAuBK,6BA+HiD,WAAA,OAAMkI,EAAK0D,aAGxF6T,cAAA,WACEjX,aAAanL,KAAK4b,UAClB5b,KAAK4b,SAAW,QAKX7X,gBAAP,SAAuBtJ,GACrB,OAAOuF,KAAKgE,MAAK,WACf,IAAI/G,EAAOK,EAAa0C,KArKb,YA4KX,GAJK/C,IACHA,EAAO,IAAIklB,EAAMniB,KAHe,iBAAXvF,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAI4S,UAAJ,oBAAkC5S,EAAlC,KAGRwC,EAAKxC,GAAQuF,uDAnIjB,OAAOgI,mCAIP,OAAOP,oCAIP,MAtDa,iBAkCX0a,CAAczf,UA4JpBlG,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAF,MAC3B+B,EAAE/B,GAAF,MAAauiB,GAAMpe,gBACnBpC,EAAE/B,GAAF,MAAWwE,YAAc+d,GACzBxgB,EAAE/B,GAAF,MAAWyE,WAAa,WAEtB,OADA1C,EAAE/B,GAAF,MAAauE,EACNge,GAAMpe,qBChNJ,CACbd,MAAAA,EACAqB,OAAAA,EACA8D,SAAAA,GACAuF,SAAAA,GACA2C,SAAAA,GACAoC,MAAAA,GACA4M,QAAAA,GACAG,UAAAA,GACAiC,IAAAA,GACAS,MAAAA,GACAzG,QAAAA", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    const hrefAttr = element.getAttribute('href')\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => (obj[0] || obj).nodeType\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ?\n      'element' :\n      toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new Error(\n        `${componentName.toUpperCase()}: ` +\n        `Option \"${property}\" provided type \"${valueType}\" ` +\n        `but expected type \"${expectedTypes}\".`)\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => function () {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = document.documentElement.dir === 'rtl'\n\nexport {\n  TRANSITION_END,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  findShadowRoot,\n  noop,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst mapData = (() => {\n  const storeData = {}\n  let id = 1\n  return {\n    set(element, key, data) {\n      if (typeof element.bsKey === 'undefined') {\n        element.bsKey = {\n          key,\n          id\n        }\n        id++\n      }\n\n      storeData[element.bsKey.id] = data\n    },\n    get(element, key) {\n      if (!element || typeof element.bsKey === 'undefined') {\n        return null\n      }\n\n      const keyProperties = element.bsKey\n      if (keyProperties.key === key) {\n        return storeData[keyProperties.id]\n      }\n\n      return null\n    },\n    delete(element, key) {\n      if (typeof element.bsKey === 'undefined') {\n        return\n      }\n\n      const keyProperties = element.bsKey\n      if (keyProperties.key === key) {\n        delete storeData[keyProperties.id]\n        delete element.bsKey\n      }\n    }\n  }\n})()\n\nconst Data = {\n  setData(instance, key, data) {\n    mapData.set(instance, key, data)\n  },\n  getData(instance, key) {\n    return mapData.get(instance, key)\n  },\n  removeData(instance, key) {\n    mapData.delete(instance, key)\n  }\n}\n\nexport default Data\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            EventHandler.off(element, event.type, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  let typeEvent = originalTypeEvent.replace(stripNameRegex, '')\n  const custom = customEvents[typeEvent]\n\n  if (custom) {\n    typeEvent = custom\n  }\n\n  const isNative = nativeEvents.has(typeEvent)\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = event.replace(stripNameRegex, '')\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.has(typeEvent)\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst VERSION = '5.0.0-beta1'\n\nclass BaseComponent {\n  constructor(element) {\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    Data.setData(element, this.constructor.DATA_KEY, this)\n  }\n\n  dispose() {\n    Data.removeData(this._element, this.constructor.DATA_KEY)\n    this._element = null\n  }\n\n  /** Static */\n\n  static getInstance(element) {\n    return Data.getData(element, this.DATA_KEY)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-bs-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASSNAME_ALERT = 'alert'\nconst CLASSNAME_FADE = 'fade'\nconst CLASSNAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  close(element) {\n    const rootElement = element ? this._getRootElement(element) : this._element\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASSNAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASSNAME_SHOW)\n\n    if (!element.classList.contains(CLASSNAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n\n    EventHandler.one(element, TRANSITION_END, () => this._destroyElement(element))\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Alert.jQueryInterface\n    $.fn[NAME].Constructor = Alert\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Alert.jQueryInterface\n    }\n  }\n})\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery, onDOMContentLoaded } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.getData(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Button.jQueryInterface\n    $.fn[NAME].Constructor = Button\n\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Button.jQueryInterface\n    }\n  }\n})\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.to<PERSON><PERSON><PERSON><PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n\n    Object.keys(element.dataset)\n      .filter(key => key.startsWith('bs'))\n      .forEach(key => {\n        let pureKey = key.replace(/^bs/, '')\n        pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n        attributes[pureKey] = normalizeData(element.dataset[key])\n      })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  matches(element, selector) {\n    return element.matches(selector)\n  },\n\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    const children = [].concat(...element.children)\n\n    return children.filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (this.matches(ancestor, selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (this.matches(next, selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst DIRECTION_NEXT = 'next'\nconst DIRECTION_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst PointerType = {\n  TOUCH: 'touch',\n  PEN: 'pen'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const direction = index > activeIndex ?\n      DIRECTION_NEXT :\n      DIRECTION_PREV\n\n    this._slide(direction, this._items[index])\n  }\n\n  dispose() {\n    super.dispose()\n    EventHandler.off(this._element, EVENT_KEY)\n\n    this._items = null\n    this._config = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    // swipe left\n    if (direction > 0) {\n      this.prev()\n    }\n\n    // swipe right\n    if (direction < 0) {\n      this.next()\n    }\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      if (event.touches && event.touches.length > 1) {\n        this.touchDeltaX = 0\n      } else {\n        this.touchDeltaX = event.touches[0].clientX - this.touchStartX\n      }\n    }\n\n    const end = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    switch (event.key) {\n      case ARROW_LEFT_KEY:\n        event.preventDefault()\n        this.prev()\n        break\n      case ARROW_RIGHT_KEY:\n        event.preventDefault()\n        this.next()\n        break\n      default:\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByDirection(direction, activeElement) {\n    const isNextDirection = direction === DIRECTION_NEXT\n    const isPrevDirection = direction === DIRECTION_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrevDirection && activeIndex === 0) ||\n                            (isNextDirection && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = direction === DIRECTION_PREV ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const indicators = SelectorEngine.find(SELECTOR_ACTIVE, this._indicatorsElement)\n\n      for (let i = 0; i < indicators.length; i++) {\n        indicators[i].classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      const nextIndicator = this._indicatorsElement.children[\n        this._getItemIndex(element)\n      ]\n\n      if (nextIndicator) {\n        nextIndicator.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(direction, element) {\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || (activeElement && this._getItemByDirection(direction, activeElement))\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    let directionalClassName\n    let orderClassName\n    let eventDirectionName\n\n    if (direction === DIRECTION_NEXT) {\n      directionalClassName = CLASS_NAME_START\n      orderClassName = CLASS_NAME_NEXT\n      eventDirectionName = DIRECTION_LEFT\n    } else {\n      directionalClassName = CLASS_NAME_END\n      orderClassName = CLASS_NAME_PREV\n      eventDirectionName = DIRECTION_RIGHT\n    }\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const transitionDuration = getTransitionDurationFromElement(activeElement)\n\n      EventHandler.one(activeElement, TRANSITION_END, () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(() => {\n          EventHandler.trigger(this._element, EVENT_SLID, {\n            relatedTarget: nextElement,\n            direction: eventDirectionName,\n            from: activeElementIndex,\n            to: nextElementIndex\n          })\n        }, 0)\n      })\n\n      emulateTransitionEnd(activeElement, transitionDuration)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-bs-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.getData(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.getData(carousels[i], DATA_KEY))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Carousel.jQueryInterface\n    $.fn[NAME].Constructor = Carousel\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Carousel.jQueryInterface\n    }\n  }\n})\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._isTransitioning = false\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-bs-target=\"#${element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-bs-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.find(elem => container !== elem)\n      activesData = tempActiveData ? Data.getData(tempActiveData, DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.setData(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n\n    emulateTransitionEnd(this._element, transitionDuration)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n    emulateTransitionEnd(this._element, transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    super.dispose()\n    this._config = null\n    this._parent = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(WIDTH) ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    if (isElement(parent)) {\n      // it's a jQuery object\n      if (typeof parent.jquery !== 'undefined' || typeof parent[0] !== 'undefined') {\n        parent = parent[0]\n      }\n    } else {\n      parent = SelectorEngine.findOne(parent)\n    }\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-bs-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (!element || !triggerArray.length) {\n      return\n    }\n\n    const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A') {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.getData(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Collapse.jQueryInterface\n    $.fn[NAME].Constructor = Collapse\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Collapse.jQueryInterface\n    }\n  }\n})\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  getElementFromSelector,\n  isElement,\n  isVisible,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_NAVBAR = 'navbar'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]'\nconst SELECTOR_FORM_CHILD = '.dropdown form'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL ? 'right-start' : 'left-start'\n\nconst Default = {\n  offset: 0,\n  flip: true,\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(number|string|function)',\n  flip: 'boolean',\n  boundary: '(string|element)',\n  reference: '(string|element)',\n  display: 'string',\n  popperConfig: '(null|object)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    Dropdown.clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Totally disable Popper for Dropdowns in Navbar\n    if (!this._inNavbar) {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      this._popper = Popper.createPopper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', null, noop()))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n  }\n\n  dispose() {\n    super.dispose()\n    EventHandler.off(this._element, EVENT_KEY)\n    this._menu = null\n\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(`.${CLASS_NAME_NAVBAR}`) !== null\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          altBoundary: this._config.flip,\n          rootBoundary: this._config.boundary\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...popperConfig,\n      ...this._config.popperConfig\n    }\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown.getParentFromElement(toggles[i])\n      const context = Data.getData(toggles[i], DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!toggles[i].classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event && ((event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName)) ||\n          (event.type === 'keyup' && event.key === TAB_KEY)) &&\n          dropdownMenu.contains(event.target)) {\n        continue\n      }\n\n      const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n      if (hideEvent.defaultPrevented) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children)\n          .forEach(elem => EventHandler.off(elem, 'mouseover', null, noop()))\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      dropdownMenu.classList.remove(CLASS_NAME_SHOW)\n      toggles[i].classList.remove(CLASS_NAME_SHOW)\n      EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || this.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this)\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (event.key === ESCAPE_KEY) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, parent).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    // Up\n    if (event.key === ARROW_UP_KEY && index > 0) {\n      index--\n    }\n\n    // Down\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) {\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  event.stopPropagation()\n  Dropdown.dropdownInterface(this, 'toggle')\n})\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_FORM_CHILD, e => e.stopPropagation())\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Dropdown.jQueryInterface\n    $.fn[NAME].Constructor = Dropdown\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Dropdown.jQueryInterface\n    }\n  }\n})\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  isRTL,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, element)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_FADE)) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, event => this.hide(event))\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n\n    if (transition) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, event => this._hideModal(event))\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    super.dispose()\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._config = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode || this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (transition) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._dialog)\n\n      EventHandler.one(this._dialog, TRANSITION_END, transitionComplete)\n      emulateTransitionEnd(this._dialog, transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    this._backdrop.parentNode.removeChild(this._backdrop)\n    this._backdrop = null\n  }\n\n  _showBackdrop(callback) {\n    const animate = this._element.classList.contains(CLASS_NAME_FADE) ?\n      CLASS_NAME_FADE :\n      ''\n\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (animate) {\n        this._backdrop.classList.add(animate)\n      }\n\n      document.body.appendChild(this._backdrop)\n\n      EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n        } else {\n          this.hide()\n        }\n      })\n\n      if (animate) {\n        reflow(this._backdrop)\n      }\n\n      this._backdrop.classList.add(CLASS_NAME_SHOW)\n\n      if (!animate) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n\n      EventHandler.one(this._backdrop, TRANSITION_END, callback)\n      emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      this._backdrop.classList.remove(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        callback()\n      }\n\n      if (this._element.classList.contains(CLASS_NAME_FADE)) {\n        const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n        EventHandler.one(this._backdrop, TRANSITION_END, callbackRemove)\n        emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else {\n      callback()\n    }\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    const modalTransitionDuration = getTransitionDurationFromElement(this._dialog)\n    EventHandler.off(this._element, TRANSITION_END)\n    EventHandler.one(this._element, TRANSITION_END, () => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      if (!isModalOverflowing) {\n        EventHandler.one(this._element, TRANSITION_END, () => {\n          this._element.style.overflowY = ''\n        })\n        emulateTransitionEnd(this._element, modalTransitionDuration)\n      }\n    })\n    emulateTransitionEnd(this._element, modalTransitionDuration)\n    this._element.focus()\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing =\n      this._element.scrollHeight > document.documentElement.clientHeight\n\n    if ((!this._isBodyOverflowing && isModalOverflowing && !isRTL) || (this._isBodyOverflowing && !isModalOverflowing && isRTL)) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if ((this._isBodyOverflowing && !isModalOverflowing && !isRTL) || (!this._isBodyOverflowing && isModalOverflowing && isRTL)) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n      //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n\n      // Adjust fixed content padding\n      SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n        .forEach(element => {\n          const actualPadding = element.style.paddingRight\n          const calculatedPadding = window.getComputedStyle(element)['padding-right']\n          Manipulator.setDataAttribute(element, 'padding-right', actualPadding)\n          element.style.paddingRight = `${Number.parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n        })\n\n      // Adjust sticky content margin\n      SelectorEngine.find(SELECTOR_STICKY_CONTENT)\n        .forEach(element => {\n          const actualMargin = element.style.marginRight\n          const calculatedMargin = window.getComputedStyle(element)['margin-right']\n          Manipulator.setDataAttribute(element, 'margin-right', actualMargin)\n          element.style.marginRight = `${Number.parseFloat(calculatedMargin) - this._scrollbarWidth}px`\n        })\n\n      // Adjust body padding\n      const actualPadding = document.body.style.paddingRight\n      const calculatedPadding = window.getComputedStyle(document.body)['padding-right']\n\n      Manipulator.setDataAttribute(document.body, 'padding-right', actualPadding)\n      document.body.style.paddingRight = `${Number.parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n    }\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n  }\n\n  _resetScrollbar() {\n    // Restore fixed content padding\n    SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n      .forEach(element => {\n        const padding = Manipulator.getDataAttribute(element, 'padding-right')\n        if (typeof padding !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'padding-right')\n          element.style.paddingRight = padding\n        }\n      })\n\n    // Restore sticky content and navbar-toggler margin\n    SelectorEngine.find(`${SELECTOR_STICKY_CONTENT}`)\n      .forEach(element => {\n        const margin = Manipulator.getDataAttribute(element, 'margin-right')\n        if (typeof margin !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'margin-right')\n          element.style.marginRight = margin\n        }\n      })\n\n    // Restore body padding\n    const padding = Manipulator.getDataAttribute(document.body, 'padding-right')\n    if (typeof padding === 'undefined') {\n      document.body.style.paddingRight = ''\n    } else {\n      Manipulator.removeDataAttribute(document.body, 'padding-right')\n      document.body.style.paddingRight = padding\n    }\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = {\n        ...Default,\n        ...Manipulator.getDataAttributes(this),\n        ...(typeof config === 'object' && config ? config : {})\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  let data = Data.getData(target, DATA_KEY)\n  if (!data) {\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n\n    data = new Modal(target, config)\n  }\n\n  data.show(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Modal.jQueryInterface\n    $.fn[NAME].Constructor = Modal\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Modal.jQueryInterface\n    }\n  }\n})\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attrName)) {\n    if (uriAttrs.has(attrName)) {\n      return Boolean(attr.nodeValue.match(SAFE_URL_PATTERN) || attr.nodeValue.match(DATA_URL_PATTERN))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (attrName.match(regExp[i])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (!allowlistKeys.includes(elName)) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  findShadowRoot,\n  getTransitionDurationFromElement,\n  getUID,\n  isElement,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultAllowlist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: '(null|array)',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  container: false,\n  fallbackPlacements: null,\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element)\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const dataKey = this.constructor.DATA_KEY\n      let context = Data.getData(event.delegateTarget, dataKey)\n\n      if (!context) {\n        context = new this.constructor(event.delegateTarget, this._getDelegateConfig())\n        Data.setData(event.delegateTarget, dataKey, context)\n      }\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n    EventHandler.off(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.config = null\n    this.tip = null\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (this.isWithContent() && this._isEnabled) {\n      const showEvent = EventHandler.trigger(this._element, this.constructor.Event.SHOW)\n      const shadowRoot = findShadowRoot(this._element)\n      const isInTheDom = shadowRoot === null ?\n        this._element.ownerDocument.documentElement.contains(this._element) :\n        shadowRoot.contains(this._element)\n\n      if (showEvent.defaultPrevented || !isInTheDom) {\n        return\n      }\n\n      const tip = this.getTipElement()\n      const tipId = getUID(this.constructor.NAME)\n\n      tip.setAttribute('id', tipId)\n      this._element.setAttribute('aria-describedby', tipId)\n\n      this.setContent()\n\n      if (this.config.animation) {\n        tip.classList.add(CLASS_NAME_FADE)\n      }\n\n      const placement = typeof this.config.placement === 'function' ?\n        this.config.placement.call(this, tip, this._element) :\n        this.config.placement\n\n      const attachment = this._getAttachment(placement)\n      this._addAttachmentClass(attachment)\n\n      const container = this._getContainer()\n      Data.setData(tip, this.constructor.DATA_KEY, this)\n\n      if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n        container.appendChild(tip)\n      }\n\n      EventHandler.trigger(this._element, this.constructor.Event.INSERTED)\n\n      this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n\n      tip.classList.add(CLASS_NAME_SHOW)\n\n      const customClass = typeof this.config.customClass === 'function' ? this.config.customClass() : this.config.customClass\n      if (customClass) {\n        tip.classList.add(...customClass.split(' '))\n      }\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children).forEach(element => {\n          EventHandler.on(element, 'mouseover', noop())\n        })\n      }\n\n      const complete = () => {\n        const prevHoverState = this._hoverState\n\n        this._hoverState = null\n        EventHandler.trigger(this._element, this.constructor.Event.SHOWN)\n\n        if (prevHoverState === HOVER_STATE_OUT) {\n          this._leave(null, this)\n        }\n      }\n\n      if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n        const transitionDuration = getTransitionDurationFromElement(this.tip)\n        EventHandler.one(this.tip, TRANSITION_END, complete)\n        emulateTransitionEnd(this.tip, transitionDuration)\n      } else {\n        complete()\n      }\n    }\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.Event.HIDDEN)\n\n      if (this._popper) {\n        this._popper.destroy()\n        this._popper = null\n      }\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(tip)\n\n      EventHandler.one(tip, TRANSITION_END, complete)\n      emulateTransitionEnd(tip, transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this.config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (typeof content === 'object' && isElement(content)) {\n      if (content.jquery) {\n        content = content[0]\n      }\n\n      // content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.allowList, this.config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this._element.getAttribute('data-bs-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this._element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  updateAttachment(attachment) {\n    if (attachment === 'right') {\n      return 'end'\n    }\n\n    if (attachment === 'left') {\n      return 'start'\n    }\n\n    return attachment\n  }\n\n  // Private\n\n  _getPopperConfig(attachment) {\n    const flipModifier = {\n      name: 'flip',\n      options: {\n        altBoundary: true\n      }\n    }\n\n    if (this.config.fallbackPlacements) {\n      flipModifier.options.fallbackPlacements = this.config.fallbackPlacements\n    }\n\n    const defaultBsConfig = {\n      placement: attachment,\n      modifiers: [\n        flipModifier,\n        {\n          name: 'preventOverflow',\n          options: {\n            rootBoundary: this.config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'onChange',\n          enabled: true,\n          phase: 'afterWrite',\n          fn: data => this._handlePopperPlacementChange(data)\n        }\n      ],\n      onFirstUpdate: data => {\n        if (data.options.placement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      }\n    }\n\n    return {\n      ...defaultBsConfig,\n      ...this.config.popperConfig\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (isElement(this.config.container)) {\n      return this.config.container\n    }\n\n    return SelectorEngine.findOne(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.Event.CLICK, this.config.selector, event => this.toggle(event)\n        )\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this._element, eventIn, this.config.selector, event => this._enter(event))\n        EventHandler.on(this._element, eventOut, this.config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n    const originalTitleType = typeof this._element.getAttribute('data-bs-original-title')\n\n    if (title || originalTitleType !== 'string') {\n      this._element.setAttribute('data-bs-original-title', title || '')\n      if (title && !this._element.getAttribute('aria-label') && !this._element.textContent) {\n        this._element.setAttribute('aria-label', title)\n      }\n\n      this._element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.delegateTarget,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.delegateTarget, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.delegateTarget,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.delegateTarget, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = false\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttr)) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    if (config && typeof config.container === 'object' && config.container.jquery) {\n      config.container = config.container[0]\n    }\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const { state } = popperData\n\n    if (!state) {\n      return\n    }\n\n    this.tip = state.elements.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(state.placement))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Tooltip.jQueryInterface\n    $.fn[NAME].Constructor = Tooltip\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Tooltip.jQueryInterface\n    }\n  }\n})\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery, onDOMContentLoaded } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n                '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this._element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContent() {\n    return this._element.getAttribute('data-bs-content') || this.config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.setData(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Popover.jQueryInterface\n    $.fn[NAME].Constructor = Popover\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Popover.jQueryInterface\n    }\n  }\n})\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n    this._scrollElement = element.tagName === 'BODY' ? window : element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS}, ${this._config.target} ${SELECTOR_LIST_ITEMS}, ${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, event => this._process(event))\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    super.dispose()\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' || scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-bs-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy, Manipulator.getDataAttributes(spy)))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = ScrollSpy.jQueryInterface\n    $.fn[NAME].Constructor = ScrollSpy\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return ScrollSpy.jQueryInterface\n    }\n  }\n})\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE)) ||\n      this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    let hideEvent = null\n\n    if (previous) {\n      hideEvent = EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      })\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented || (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(this._element, listElement)\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(element, active, callback)\n\n    if (active && isTransitioning) {\n      const transitionDuration = getTransitionDurationFromElement(active)\n      active.classList.remove(CLASS_NAME_SHOW)\n\n      EventHandler.one(active, TRANSITION_END, complete)\n      emulateTransitionEnd(active, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    if (element.parentNode && element.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.getData(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n\n  const data = Data.getData(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Tab.jQueryInterface\n    $.fn[NAME].Constructor = Tab\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Tab.jQueryInterface\n    }\n  }\n})\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getTransitionDurationFromElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n\n    super.dispose()\n    this._config = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Toast.jQueryInterface\n    $.fn[NAME].Constructor = Toast\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Toast.jQueryInterface\n    }\n  }\n})\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"]}