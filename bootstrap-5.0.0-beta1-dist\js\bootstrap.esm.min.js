/*!
  * Bootstrap v5.0.0-beta1 (https://getbootstrap.com/)
  * Copyright 2011-2020 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */
import*as <PERSON><PERSON> from"@popperjs/core";import{createPopper}from"@popperjs/core";function _defineProperties(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),e}function _extends(){return(_extends=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}function _inheritsLoose(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}var MAX_UID=1e6,MILLISECONDS_MULTIPLIER=1e3,TRANSITION_END="transitionend",toType=function(e){return null==e?""+e:{}.toString.call(e).match(/\s([a-z]+)/i)[1].toLowerCase()},getUID=function(e){do{e+=Math.floor(Math.random()*MAX_UID)}while(document.getElementById(e));return e},getSelector=function(e){var t=e.getAttribute("data-bs-target");if(!t||"#"===t){var n=e.getAttribute("href");t=n&&"#"!==n?n.trim():null}return t},getSelectorFromElement=function(e){var t=getSelector(e);return t&&document.querySelector(t)?t:null},getElementFromSelector=function(e){var t=getSelector(e);return t?document.querySelector(t):null},getTransitionDurationFromElement=function(e){if(!e)return 0;var t=window.getComputedStyle(e),n=t.transitionDuration,i=t.transitionDelay,o=Number.parseFloat(n),r=Number.parseFloat(i);return o||r?(n=n.split(",")[0],i=i.split(",")[0],(Number.parseFloat(n)+Number.parseFloat(i))*MILLISECONDS_MULTIPLIER):0},triggerTransitionEnd=function(e){e.dispatchEvent(new Event(TRANSITION_END))},isElement=function(e){return(e[0]||e).nodeType},emulateTransitionEnd=function(e,t){var n=!1,i=t+5;e.addEventListener(TRANSITION_END,(function t(){n=!0,e.removeEventListener(TRANSITION_END,t)})),setTimeout((function(){n||triggerTransitionEnd(e)}),i)},typeCheckConfig=function(e,t,n){Object.keys(n).forEach((function(i){var o=n[i],r=t[i],a=r&&isElement(r)?"element":toType(r);if(!new RegExp(o).test(a))throw new Error(e.toUpperCase()+': Option "'+i+'" provided type "'+a+'" but expected type "'+o+'".')}))},isVisible=function(e){if(!e)return!1;if(e.style&&e.parentNode&&e.parentNode.style){var t=getComputedStyle(e),n=getComputedStyle(e.parentNode);return"none"!==t.display&&"none"!==n.display&&"hidden"!==t.visibility}return!1},findShadowRoot=function e(t){if(!document.documentElement.attachShadow)return null;if("function"==typeof t.getRootNode){var n=t.getRootNode();return n instanceof ShadowRoot?n:null}return t instanceof ShadowRoot?t:t.parentNode?e(t.parentNode):null},noop=function(){return function(){}},reflow=function(e){return e.offsetHeight},getjQuery=function(){var e=window.jQuery;return e&&!document.body.hasAttribute("data-bs-no-jquery")?e:null},onDOMContentLoaded=function(e){"loading"===document.readyState?document.addEventListener("DOMContentLoaded",e):e()},isRTL="rtl"===document.documentElement.dir,mapData=function(){var e={},t=1;return{set:function(n,i,o){void 0===n.bsKey&&(n.bsKey={key:i,id:t},t++),e[n.bsKey.id]=o},get:function(t,n){if(!t||void 0===t.bsKey)return null;var i=t.bsKey;return i.key===n?e[i.id]:null},delete:function(t,n){if(void 0!==t.bsKey){var i=t.bsKey;i.key===n&&(delete e[i.id],delete t.bsKey)}}}}(),Data={setData:function(e,t,n){mapData.set(e,t,n)},getData:function(e,t){return mapData.get(e,t)},removeData:function(e,t){mapData.delete(e,t)}},namespaceRegex=/[^.]*(?=\..*)\.|.*/,stripNameRegex=/\..*/,stripUidRegex=/::\d+$/,eventRegistry={},uidEvent=1,customEvents={mouseenter:"mouseover",mouseleave:"mouseout"},nativeEvents=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function getUidEvent(e,t){return t&&t+"::"+uidEvent++||e.uidEvent||uidEvent++}function getEvent(e){var t=getUidEvent(e);return e.uidEvent=t,eventRegistry[t]=eventRegistry[t]||{},eventRegistry[t]}function bootstrapHandler(e,t){return function n(i){return i.delegateTarget=e,n.oneOff&&EventHandler.off(e,i.type,t),t.apply(e,[i])}}function bootstrapDelegationHandler(e,t,n){return function i(o){for(var r=e.querySelectorAll(t),a=o.target;a&&a!==this;a=a.parentNode)for(var s=r.length;s--;)if(r[s]===a)return o.delegateTarget=a,i.oneOff&&EventHandler.off(e,o.type,n),n.apply(a,[o]);return null}}function findHandler(e,t,n){void 0===n&&(n=null);for(var i=Object.keys(e),o=0,r=i.length;o<r;o++){var a=e[i[o]];if(a.originalHandler===t&&a.delegationSelector===n)return a}return null}function normalizeParams(e,t,n){var i="string"==typeof t,o=i?n:t,r=e.replace(stripNameRegex,""),a=customEvents[r];return a&&(r=a),nativeEvents.has(r)||(r=e),[i,o,r]}function addHandler(e,t,n,i,o){if("string"==typeof t&&e){n||(n=i,i=null);var r=normalizeParams(t,n,i),a=r[0],s=r[1],l=r[2],E=getEvent(e),_=E[l]||(E[l]={}),c=findHandler(_,s,a?n:null);if(c)c.oneOff=c.oneOff&&o;else{var u=getUidEvent(s,t.replace(namespaceRegex,"")),d=a?bootstrapDelegationHandler(e,n,i):bootstrapHandler(e,n);d.delegationSelector=a?n:null,d.originalHandler=s,d.oneOff=o,d.uidEvent=u,_[u]=d,e.addEventListener(l,d,a)}}}function removeHandler(e,t,n,i,o){var r=findHandler(t[n],i,o);r&&(e.removeEventListener(n,r,Boolean(o)),delete t[n][r.uidEvent])}function removeNamespacedHandlers(e,t,n,i){var o=t[n]||{};Object.keys(o).forEach((function(r){if(r.includes(i)){var a=o[r];removeHandler(e,t,n,a.originalHandler,a.delegationSelector)}}))}var EventHandler={on:function(e,t,n,i){addHandler(e,t,n,i,!1)},one:function(e,t,n,i){addHandler(e,t,n,i,!0)},off:function(e,t,n,i){if("string"==typeof t&&e){var o=normalizeParams(t,n,i),r=o[0],a=o[1],s=o[2],l=s!==t,E=getEvent(e),_=t.startsWith(".");if(void 0===a){_&&Object.keys(E).forEach((function(n){removeNamespacedHandlers(e,E,n,t.slice(1))}));var c=E[s]||{};Object.keys(c).forEach((function(n){var i=n.replace(stripUidRegex,"");if(!l||t.includes(i)){var o=c[n];removeHandler(e,E,s,o.originalHandler,o.delegationSelector)}}))}else{if(!E||!E[s])return;removeHandler(e,E,s,a,r?n:null)}}},trigger:function(e,t,n){if("string"!=typeof t||!e)return null;var i,o=getjQuery(),r=t.replace(stripNameRegex,""),a=t!==r,s=nativeEvents.has(r),l=!0,E=!0,_=!1,c=null;return a&&o&&(i=o.Event(t,n),o(e).trigger(i),l=!i.isPropagationStopped(),E=!i.isImmediatePropagationStopped(),_=i.isDefaultPrevented()),s?(c=document.createEvent("HTMLEvents")).initEvent(r,l,!0):c=new CustomEvent(t,{bubbles:l,cancelable:!0}),void 0!==n&&Object.keys(n).forEach((function(e){Object.defineProperty(c,e,{get:function(){return n[e]}})})),_&&c.preventDefault(),E&&e.dispatchEvent(c),c.defaultPrevented&&void 0!==i&&i.preventDefault(),c}},VERSION="5.0.0-beta1",BaseComponent=function(){function e(e){e&&(this._element=e,Data.setData(e,this.constructor.DATA_KEY,this))}return e.prototype.dispose=function(){Data.removeData(this._element,this.constructor.DATA_KEY),this._element=null},e.getInstance=function(e){return Data.getData(e,this.DATA_KEY)},_createClass(e,null,[{key:"VERSION",get:function(){return VERSION}}]),e}(),NAME="alert",DATA_KEY="bs.alert",EVENT_KEY="."+DATA_KEY,DATA_API_KEY=".data-api",SELECTOR_DISMISS='[data-bs-dismiss="alert"]',EVENT_CLOSE="close"+EVENT_KEY,EVENT_CLOSED="closed"+EVENT_KEY,EVENT_CLICK_DATA_API="click"+EVENT_KEY+DATA_API_KEY,CLASSNAME_ALERT="alert",CLASSNAME_FADE="fade",CLASSNAME_SHOW="show",Alert=function(e){function t(){return e.apply(this,arguments)||this}_inheritsLoose(t,e);var n=t.prototype;return n.close=function(e){var t=e?this._getRootElement(e):this._element,n=this._triggerCloseEvent(t);null===n||n.defaultPrevented||this._removeElement(t)},n._getRootElement=function(e){return getElementFromSelector(e)||e.closest("."+CLASSNAME_ALERT)},n._triggerCloseEvent=function(e){return EventHandler.trigger(e,EVENT_CLOSE)},n._removeElement=function(e){var t=this;if(e.classList.remove(CLASSNAME_SHOW),e.classList.contains(CLASSNAME_FADE)){var n=getTransitionDurationFromElement(e);EventHandler.one(e,TRANSITION_END,(function(){return t._destroyElement(e)})),emulateTransitionEnd(e,n)}else this._destroyElement(e)},n._destroyElement=function(e){e.parentNode&&e.parentNode.removeChild(e),EventHandler.trigger(e,EVENT_CLOSED)},t.jQueryInterface=function(e){return this.each((function(){var n=Data.getData(this,DATA_KEY);n||(n=new t(this)),"close"===e&&n[e](this)}))},t.handleDismiss=function(e){return function(t){t&&t.preventDefault(),e.close(this)}},_createClass(t,null,[{key:"DATA_KEY",get:function(){return DATA_KEY}}]),t}(BaseComponent);EventHandler.on(document,EVENT_CLICK_DATA_API,SELECTOR_DISMISS,Alert.handleDismiss(new Alert)),onDOMContentLoaded((function(){var e=getjQuery();if(e){var t=e.fn[NAME];e.fn[NAME]=Alert.jQueryInterface,e.fn[NAME].Constructor=Alert,e.fn[NAME].noConflict=function(){return e.fn[NAME]=t,Alert.jQueryInterface}}}));var NAME$1="button",DATA_KEY$1="bs.button",EVENT_KEY$1="."+DATA_KEY$1,DATA_API_KEY$1=".data-api",CLASS_NAME_ACTIVE="active",SELECTOR_DATA_TOGGLE='[data-bs-toggle="button"]',EVENT_CLICK_DATA_API$1="click"+EVENT_KEY$1+DATA_API_KEY$1,Button=function(e){function t(){return e.apply(this,arguments)||this}return _inheritsLoose(t,e),t.prototype.toggle=function(){this._element.setAttribute("aria-pressed",this._element.classList.toggle(CLASS_NAME_ACTIVE))},t.jQueryInterface=function(e){return this.each((function(){var n=Data.getData(this,DATA_KEY$1);n||(n=new t(this)),"toggle"===e&&n[e]()}))},_createClass(t,null,[{key:"DATA_KEY",get:function(){return DATA_KEY$1}}]),t}(BaseComponent);function normalizeData(e){return"true"===e||"false"!==e&&(e===Number(e).toString()?Number(e):""===e||"null"===e?null:e)}function normalizeDataKey(e){return e.replace(/[A-Z]/g,(function(e){return"-"+e.toLowerCase()}))}EventHandler.on(document,EVENT_CLICK_DATA_API$1,SELECTOR_DATA_TOGGLE,(function(e){e.preventDefault();var t=e.target.closest(SELECTOR_DATA_TOGGLE),n=Data.getData(t,DATA_KEY$1);n||(n=new Button(t)),n.toggle()})),onDOMContentLoaded((function(){var e=getjQuery();if(e){var t=e.fn[NAME$1];e.fn[NAME$1]=Button.jQueryInterface,e.fn[NAME$1].Constructor=Button,e.fn[NAME$1].noConflict=function(){return e.fn[NAME$1]=t,Button.jQueryInterface}}}));var Manipulator={setDataAttribute:function(e,t,n){e.setAttribute("data-bs-"+normalizeDataKey(t),n)},removeDataAttribute:function(e,t){e.removeAttribute("data-bs-"+normalizeDataKey(t))},getDataAttributes:function(e){if(!e)return{};var t={};return Object.keys(e.dataset).filter((function(e){return e.startsWith("bs")})).forEach((function(n){var i=n.replace(/^bs/,"");i=i.charAt(0).toLowerCase()+i.slice(1,i.length),t[i]=normalizeData(e.dataset[n])})),t},getDataAttribute:function(e,t){return normalizeData(e.getAttribute("data-bs-"+normalizeDataKey(t)))},offset:function(e){var t=e.getBoundingClientRect();return{top:t.top+document.body.scrollTop,left:t.left+document.body.scrollLeft}},position:function(e){return{top:e.offsetTop,left:e.offsetLeft}}},NODE_TEXT=3,SelectorEngine={matches:function(e,t){return e.matches(t)},find:function(e,t){var n;return void 0===t&&(t=document.documentElement),(n=[]).concat.apply(n,Element.prototype.querySelectorAll.call(t,e))},findOne:function(e,t){return void 0===t&&(t=document.documentElement),Element.prototype.querySelector.call(t,e)},children:function(e,t){var n,i=(n=[]).concat.apply(n,e.children);return i.filter((function(e){return e.matches(t)}))},parents:function(e,t){for(var n=[],i=e.parentNode;i&&i.nodeType===Node.ELEMENT_NODE&&i.nodeType!==NODE_TEXT;)this.matches(i,t)&&n.push(i),i=i.parentNode;return n},prev:function(e,t){for(var n=e.previousElementSibling;n;){if(n.matches(t))return[n];n=n.previousElementSibling}return[]},next:function(e,t){for(var n=e.nextElementSibling;n;){if(this.matches(n,t))return[n];n=n.nextElementSibling}return[]}},NAME$2="carousel",DATA_KEY$2="bs.carousel",EVENT_KEY$2="."+DATA_KEY$2,DATA_API_KEY$2=".data-api",ARROW_LEFT_KEY="ArrowLeft",ARROW_RIGHT_KEY="ArrowRight",TOUCHEVENT_COMPAT_WAIT=500,SWIPE_THRESHOLD=40,Default={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},DefaultType={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},DIRECTION_NEXT="next",DIRECTION_PREV="prev",DIRECTION_LEFT="left",DIRECTION_RIGHT="right",EVENT_SLIDE="slide"+EVENT_KEY$2,EVENT_SLID="slid"+EVENT_KEY$2,EVENT_KEYDOWN="keydown"+EVENT_KEY$2,EVENT_MOUSEENTER="mouseenter"+EVENT_KEY$2,EVENT_MOUSELEAVE="mouseleave"+EVENT_KEY$2,EVENT_TOUCHSTART="touchstart"+EVENT_KEY$2,EVENT_TOUCHMOVE="touchmove"+EVENT_KEY$2,EVENT_TOUCHEND="touchend"+EVENT_KEY$2,EVENT_POINTERDOWN="pointerdown"+EVENT_KEY$2,EVENT_POINTERUP="pointerup"+EVENT_KEY$2,EVENT_DRAG_START="dragstart"+EVENT_KEY$2,EVENT_LOAD_DATA_API="load"+EVENT_KEY$2+DATA_API_KEY$2,EVENT_CLICK_DATA_API$2="click"+EVENT_KEY$2+DATA_API_KEY$2,CLASS_NAME_CAROUSEL="carousel",CLASS_NAME_ACTIVE$1="active",CLASS_NAME_SLIDE="slide",CLASS_NAME_END="carousel-item-end",CLASS_NAME_START="carousel-item-start",CLASS_NAME_NEXT="carousel-item-next",CLASS_NAME_PREV="carousel-item-prev",CLASS_NAME_POINTER_EVENT="pointer-event",SELECTOR_ACTIVE=".active",SELECTOR_ACTIVE_ITEM=".active.carousel-item",SELECTOR_ITEM=".carousel-item",SELECTOR_ITEM_IMG=".carousel-item img",SELECTOR_NEXT_PREV=".carousel-item-next, .carousel-item-prev",SELECTOR_INDICATORS=".carousel-indicators",SELECTOR_DATA_SLIDE="[data-bs-slide], [data-bs-slide-to]",SELECTOR_DATA_RIDE='[data-bs-ride="carousel"]',PointerType={TOUCH:"touch",PEN:"pen"},Carousel=function(e){function t(t,n){var i;return(i=e.call(this,t)||this)._items=null,i._interval=null,i._activeElement=null,i._isPaused=!1,i._isSliding=!1,i.touchTimeout=null,i.touchStartX=0,i.touchDeltaX=0,i._config=i._getConfig(n),i._indicatorsElement=SelectorEngine.findOne(SELECTOR_INDICATORS,i._element),i._touchSupported="ontouchstart"in document.documentElement||navigator.maxTouchPoints>0,i._pointerEvent=Boolean(window.PointerEvent),i._addEventListeners(),i}_inheritsLoose(t,e);var n=t.prototype;return n.next=function(){this._isSliding||this._slide(DIRECTION_NEXT)},n.nextWhenVisible=function(){!document.hidden&&isVisible(this._element)&&this.next()},n.prev=function(){this._isSliding||this._slide(DIRECTION_PREV)},n.pause=function(e){e||(this._isPaused=!0),SelectorEngine.findOne(SELECTOR_NEXT_PREV,this._element)&&(triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},n.cycle=function(e){e||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config&&this._config.interval&&!this._isPaused&&(this._updateInterval(),this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},n.to=function(e){var t=this;this._activeElement=SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM,this._element);var n=this._getItemIndex(this._activeElement);if(!(e>this._items.length-1||e<0))if(this._isSliding)EventHandler.one(this._element,EVENT_SLID,(function(){return t.to(e)}));else{if(n===e)return this.pause(),void this.cycle();var i=e>n?DIRECTION_NEXT:DIRECTION_PREV;this._slide(i,this._items[e])}},n.dispose=function(){e.prototype.dispose.call(this),EventHandler.off(this._element,EVENT_KEY$2),this._items=null,this._config=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},n._getConfig=function(e){return e=_extends({},Default,e),typeCheckConfig(NAME$2,e,DefaultType),e},n._handleSwipe=function(){var e=Math.abs(this.touchDeltaX);if(!(e<=SWIPE_THRESHOLD)){var t=e/this.touchDeltaX;this.touchDeltaX=0,t>0&&this.prev(),t<0&&this.next()}},n._addEventListeners=function(){var e=this;this._config.keyboard&&EventHandler.on(this._element,EVENT_KEYDOWN,(function(t){return e._keydown(t)})),"hover"===this._config.pause&&(EventHandler.on(this._element,EVENT_MOUSEENTER,(function(t){return e.pause(t)})),EventHandler.on(this._element,EVENT_MOUSELEAVE,(function(t){return e.cycle(t)}))),this._config.touch&&this._touchSupported&&this._addTouchEventListeners()},n._addTouchEventListeners=function(){var e=this,t=function(t){e._pointerEvent&&PointerType[t.pointerType.toUpperCase()]?e.touchStartX=t.clientX:e._pointerEvent||(e.touchStartX=t.touches[0].clientX)},n=function(t){e._pointerEvent&&PointerType[t.pointerType.toUpperCase()]&&(e.touchDeltaX=t.clientX-e.touchStartX),e._handleSwipe(),"hover"===e._config.pause&&(e.pause(),e.touchTimeout&&clearTimeout(e.touchTimeout),e.touchTimeout=setTimeout((function(t){return e.cycle(t)}),TOUCHEVENT_COMPAT_WAIT+e._config.interval))};SelectorEngine.find(SELECTOR_ITEM_IMG,this._element).forEach((function(e){EventHandler.on(e,EVENT_DRAG_START,(function(e){return e.preventDefault()}))})),this._pointerEvent?(EventHandler.on(this._element,EVENT_POINTERDOWN,(function(e){return t(e)})),EventHandler.on(this._element,EVENT_POINTERUP,(function(e){return n(e)})),this._element.classList.add(CLASS_NAME_POINTER_EVENT)):(EventHandler.on(this._element,EVENT_TOUCHSTART,(function(e){return t(e)})),EventHandler.on(this._element,EVENT_TOUCHMOVE,(function(t){return function(t){t.touches&&t.touches.length>1?e.touchDeltaX=0:e.touchDeltaX=t.touches[0].clientX-e.touchStartX}(t)})),EventHandler.on(this._element,EVENT_TOUCHEND,(function(e){return n(e)})))},n._keydown=function(e){if(!/input|textarea/i.test(e.target.tagName))switch(e.key){case ARROW_LEFT_KEY:e.preventDefault(),this.prev();break;case ARROW_RIGHT_KEY:e.preventDefault(),this.next()}},n._getItemIndex=function(e){return this._items=e&&e.parentNode?SelectorEngine.find(SELECTOR_ITEM,e.parentNode):[],this._items.indexOf(e)},n._getItemByDirection=function(e,t){var n=e===DIRECTION_NEXT,i=e===DIRECTION_PREV,o=this._getItemIndex(t),r=this._items.length-1;if((i&&0===o||n&&o===r)&&!this._config.wrap)return t;var a=(o+(e===DIRECTION_PREV?-1:1))%this._items.length;return-1===a?this._items[this._items.length-1]:this._items[a]},n._triggerSlideEvent=function(e,t){var n=this._getItemIndex(e),i=this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM,this._element));return EventHandler.trigger(this._element,EVENT_SLIDE,{relatedTarget:e,direction:t,from:i,to:n})},n._setActiveIndicatorElement=function(e){if(this._indicatorsElement){for(var t=SelectorEngine.find(SELECTOR_ACTIVE,this._indicatorsElement),n=0;n<t.length;n++)t[n].classList.remove(CLASS_NAME_ACTIVE$1);var i=this._indicatorsElement.children[this._getItemIndex(e)];i&&i.classList.add(CLASS_NAME_ACTIVE$1)}},n._updateInterval=function(){var e=this._activeElement||SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM,this._element);if(e){var t=Number.parseInt(e.getAttribute("data-bs-interval"),10);t?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=t):this._config.interval=this._config.defaultInterval||this._config.interval}},n._slide=function(e,t){var n,i,o,r=this,a=SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM,this._element),s=this._getItemIndex(a),l=t||a&&this._getItemByDirection(e,a),E=this._getItemIndex(l),_=Boolean(this._interval);if(e===DIRECTION_NEXT?(n=CLASS_NAME_START,i=CLASS_NAME_NEXT,o=DIRECTION_LEFT):(n=CLASS_NAME_END,i=CLASS_NAME_PREV,o=DIRECTION_RIGHT),l&&l.classList.contains(CLASS_NAME_ACTIVE$1))this._isSliding=!1;else if(!this._triggerSlideEvent(l,o).defaultPrevented&&a&&l){if(this._isSliding=!0,_&&this.pause(),this._setActiveIndicatorElement(l),this._activeElement=l,this._element.classList.contains(CLASS_NAME_SLIDE)){l.classList.add(i),reflow(l),a.classList.add(n),l.classList.add(n);var c=getTransitionDurationFromElement(a);EventHandler.one(a,TRANSITION_END,(function(){l.classList.remove(n,i),l.classList.add(CLASS_NAME_ACTIVE$1),a.classList.remove(CLASS_NAME_ACTIVE$1,i,n),r._isSliding=!1,setTimeout((function(){EventHandler.trigger(r._element,EVENT_SLID,{relatedTarget:l,direction:o,from:s,to:E})}),0)})),emulateTransitionEnd(a,c)}else a.classList.remove(CLASS_NAME_ACTIVE$1),l.classList.add(CLASS_NAME_ACTIVE$1),this._isSliding=!1,EventHandler.trigger(this._element,EVENT_SLID,{relatedTarget:l,direction:o,from:s,to:E});_&&this.cycle()}},t.carouselInterface=function(e,n){var i=Data.getData(e,DATA_KEY$2),o=_extends({},Default,Manipulator.getDataAttributes(e));"object"==typeof n&&(o=_extends({},o,n));var r="string"==typeof n?n:o.slide;if(i||(i=new t(e,o)),"number"==typeof n)i.to(n);else if("string"==typeof r){if(void 0===i[r])throw new TypeError('No method named "'+r+'"');i[r]()}else o.interval&&o.ride&&(i.pause(),i.cycle())},t.jQueryInterface=function(e){return this.each((function(){t.carouselInterface(this,e)}))},t.dataApiClickHandler=function(e){var n=getElementFromSelector(this);if(n&&n.classList.contains(CLASS_NAME_CAROUSEL)){var i=_extends({},Manipulator.getDataAttributes(n),Manipulator.getDataAttributes(this)),o=this.getAttribute("data-bs-slide-to");o&&(i.interval=!1),t.carouselInterface(n,i),o&&Data.getData(n,DATA_KEY$2).to(o),e.preventDefault()}},_createClass(t,null,[{key:"Default",get:function(){return Default}},{key:"DATA_KEY",get:function(){return DATA_KEY$2}}]),t}(BaseComponent);EventHandler.on(document,EVENT_CLICK_DATA_API$2,SELECTOR_DATA_SLIDE,Carousel.dataApiClickHandler),EventHandler.on(window,EVENT_LOAD_DATA_API,(function(){for(var e=SelectorEngine.find(SELECTOR_DATA_RIDE),t=0,n=e.length;t<n;t++)Carousel.carouselInterface(e[t],Data.getData(e[t],DATA_KEY$2))})),onDOMContentLoaded((function(){var e=getjQuery();if(e){var t=e.fn[NAME$2];e.fn[NAME$2]=Carousel.jQueryInterface,e.fn[NAME$2].Constructor=Carousel,e.fn[NAME$2].noConflict=function(){return e.fn[NAME$2]=t,Carousel.jQueryInterface}}}));var NAME$3="collapse",DATA_KEY$3="bs.collapse",EVENT_KEY$3="."+DATA_KEY$3,DATA_API_KEY$3=".data-api",Default$1={toggle:!0,parent:""},DefaultType$1={toggle:"boolean",parent:"(string|element)"},EVENT_SHOW="show"+EVENT_KEY$3,EVENT_SHOWN="shown"+EVENT_KEY$3,EVENT_HIDE="hide"+EVENT_KEY$3,EVENT_HIDDEN="hidden"+EVENT_KEY$3,EVENT_CLICK_DATA_API$3="click"+EVENT_KEY$3+DATA_API_KEY$3,CLASS_NAME_SHOW="show",CLASS_NAME_COLLAPSE="collapse",CLASS_NAME_COLLAPSING="collapsing",CLASS_NAME_COLLAPSED="collapsed",WIDTH="width",HEIGHT="height",SELECTOR_ACTIVES=".show, .collapsing",SELECTOR_DATA_TOGGLE$1='[data-bs-toggle="collapse"]',Collapse=function(e){function t(t,n){var i;(i=e.call(this,t)||this)._isTransitioning=!1,i._config=i._getConfig(n),i._triggerArray=SelectorEngine.find(SELECTOR_DATA_TOGGLE$1+'[href="#'+t.id+'"],'+SELECTOR_DATA_TOGGLE$1+'[data-bs-target="#'+t.id+'"]');for(var o=SelectorEngine.find(SELECTOR_DATA_TOGGLE$1),r=0,a=o.length;r<a;r++){var s=o[r],l=getSelectorFromElement(s),E=SelectorEngine.find(l).filter((function(e){return e===t}));null!==l&&E.length&&(i._selector=l,i._triggerArray.push(s))}return i._parent=i._config.parent?i._getParent():null,i._config.parent||i._addAriaAndCollapsedClass(i._element,i._triggerArray),i._config.toggle&&i.toggle(),i}_inheritsLoose(t,e);var n=t.prototype;return n.toggle=function(){this._element.classList.contains(CLASS_NAME_SHOW)?this.hide():this.show()},n.show=function(){var e=this;if(!this._isTransitioning&&!this._element.classList.contains(CLASS_NAME_SHOW)){var n,i;this._parent&&0===(n=SelectorEngine.find(SELECTOR_ACTIVES,this._parent).filter((function(t){return"string"==typeof e._config.parent?t.getAttribute("data-bs-parent")===e._config.parent:t.classList.contains(CLASS_NAME_COLLAPSE)}))).length&&(n=null);var o=SelectorEngine.findOne(this._selector);if(n){var r=n.find((function(e){return o!==e}));if((i=r?Data.getData(r,DATA_KEY$3):null)&&i._isTransitioning)return}if(!EventHandler.trigger(this._element,EVENT_SHOW).defaultPrevented){n&&n.forEach((function(e){o!==e&&t.collapseInterface(e,"hide"),i||Data.setData(e,DATA_KEY$3,null)}));var a=this._getDimension();this._element.classList.remove(CLASS_NAME_COLLAPSE),this._element.classList.add(CLASS_NAME_COLLAPSING),this._element.style[a]=0,this._triggerArray.length&&this._triggerArray.forEach((function(e){e.classList.remove(CLASS_NAME_COLLAPSED),e.setAttribute("aria-expanded",!0)})),this.setTransitioning(!0);var s="scroll"+(a[0].toUpperCase()+a.slice(1)),l=getTransitionDurationFromElement(this._element);EventHandler.one(this._element,TRANSITION_END,(function(){e._element.classList.remove(CLASS_NAME_COLLAPSING),e._element.classList.add(CLASS_NAME_COLLAPSE,CLASS_NAME_SHOW),e._element.style[a]="",e.setTransitioning(!1),EventHandler.trigger(e._element,EVENT_SHOWN)})),emulateTransitionEnd(this._element,l),this._element.style[a]=this._element[s]+"px"}}},n.hide=function(){var e=this;if(!this._isTransitioning&&this._element.classList.contains(CLASS_NAME_SHOW)&&!EventHandler.trigger(this._element,EVENT_HIDE).defaultPrevented){var t=this._getDimension();this._element.style[t]=this._element.getBoundingClientRect()[t]+"px",reflow(this._element),this._element.classList.add(CLASS_NAME_COLLAPSING),this._element.classList.remove(CLASS_NAME_COLLAPSE,CLASS_NAME_SHOW);var n=this._triggerArray.length;if(n>0)for(var i=0;i<n;i++){var o=this._triggerArray[i],r=getElementFromSelector(o);r&&!r.classList.contains(CLASS_NAME_SHOW)&&(o.classList.add(CLASS_NAME_COLLAPSED),o.setAttribute("aria-expanded",!1))}this.setTransitioning(!0);this._element.style[t]="";var a=getTransitionDurationFromElement(this._element);EventHandler.one(this._element,TRANSITION_END,(function(){e.setTransitioning(!1),e._element.classList.remove(CLASS_NAME_COLLAPSING),e._element.classList.add(CLASS_NAME_COLLAPSE),EventHandler.trigger(e._element,EVENT_HIDDEN)})),emulateTransitionEnd(this._element,a)}},n.setTransitioning=function(e){this._isTransitioning=e},n.dispose=function(){e.prototype.dispose.call(this),this._config=null,this._parent=null,this._triggerArray=null,this._isTransitioning=null},n._getConfig=function(e){return(e=_extends({},Default$1,e)).toggle=Boolean(e.toggle),typeCheckConfig(NAME$3,e,DefaultType$1),e},n._getDimension=function(){return this._element.classList.contains(WIDTH)?WIDTH:HEIGHT},n._getParent=function(){var e=this,t=this._config.parent;isElement(t)?void 0===t.jquery&&void 0===t[0]||(t=t[0]):t=SelectorEngine.findOne(t);var n=SELECTOR_DATA_TOGGLE$1+'[data-bs-parent="'+t+'"]';return SelectorEngine.find(n,t).forEach((function(t){var n=getElementFromSelector(t);e._addAriaAndCollapsedClass(n,[t])})),t},n._addAriaAndCollapsedClass=function(e,t){if(e&&t.length){var n=e.classList.contains(CLASS_NAME_SHOW);t.forEach((function(e){n?e.classList.remove(CLASS_NAME_COLLAPSED):e.classList.add(CLASS_NAME_COLLAPSED),e.setAttribute("aria-expanded",n)}))}},t.collapseInterface=function(e,n){var i=Data.getData(e,DATA_KEY$3),o=_extends({},Default$1,Manipulator.getDataAttributes(e),"object"==typeof n&&n?n:{});if(!i&&o.toggle&&"string"==typeof n&&/show|hide/.test(n)&&(o.toggle=!1),i||(i=new t(e,o)),"string"==typeof n){if(void 0===i[n])throw new TypeError('No method named "'+n+'"');i[n]()}},t.jQueryInterface=function(e){return this.each((function(){t.collapseInterface(this,e)}))},_createClass(t,null,[{key:"Default",get:function(){return Default$1}},{key:"DATA_KEY",get:function(){return DATA_KEY$3}}]),t}(BaseComponent);EventHandler.on(document,EVENT_CLICK_DATA_API$3,SELECTOR_DATA_TOGGLE$1,(function(e){"A"===e.target.tagName&&e.preventDefault();var t=Manipulator.getDataAttributes(this),n=getSelectorFromElement(this);SelectorEngine.find(n).forEach((function(e){var n,i=Data.getData(e,DATA_KEY$3);i?(null===i._parent&&"string"==typeof t.parent&&(i._config.parent=t.parent,i._parent=i._getParent()),n="toggle"):n=t,Collapse.collapseInterface(e,n)}))})),onDOMContentLoaded((function(){var e=getjQuery();if(e){var t=e.fn[NAME$3];e.fn[NAME$3]=Collapse.jQueryInterface,e.fn[NAME$3].Constructor=Collapse,e.fn[NAME$3].noConflict=function(){return e.fn[NAME$3]=t,Collapse.jQueryInterface}}}));var NAME$4="dropdown",DATA_KEY$4="bs.dropdown",EVENT_KEY$4="."+DATA_KEY$4,DATA_API_KEY$4=".data-api",ESCAPE_KEY="Escape",SPACE_KEY="Space",TAB_KEY="Tab",ARROW_UP_KEY="ArrowUp",ARROW_DOWN_KEY="ArrowDown",RIGHT_MOUSE_BUTTON=2,REGEXP_KEYDOWN=new RegExp(ARROW_UP_KEY+"|"+ARROW_DOWN_KEY+"|"+ESCAPE_KEY),EVENT_HIDE$1="hide"+EVENT_KEY$4,EVENT_HIDDEN$1="hidden"+EVENT_KEY$4,EVENT_SHOW$1="show"+EVENT_KEY$4,EVENT_SHOWN$1="shown"+EVENT_KEY$4,EVENT_CLICK="click"+EVENT_KEY$4,EVENT_CLICK_DATA_API$4="click"+EVENT_KEY$4+DATA_API_KEY$4,EVENT_KEYDOWN_DATA_API="keydown"+EVENT_KEY$4+DATA_API_KEY$4,EVENT_KEYUP_DATA_API="keyup"+EVENT_KEY$4+DATA_API_KEY$4,CLASS_NAME_DISABLED="disabled",CLASS_NAME_SHOW$1="show",CLASS_NAME_DROPUP="dropup",CLASS_NAME_DROPEND="dropend",CLASS_NAME_DROPSTART="dropstart",CLASS_NAME_NAVBAR="navbar",SELECTOR_DATA_TOGGLE$2='[data-bs-toggle="dropdown"]',SELECTOR_FORM_CHILD=".dropdown form",SELECTOR_MENU=".dropdown-menu",SELECTOR_NAVBAR_NAV=".navbar-nav",SELECTOR_VISIBLE_ITEMS=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",PLACEMENT_TOP=isRTL?"top-end":"top-start",PLACEMENT_TOPEND=isRTL?"top-start":"top-end",PLACEMENT_BOTTOM=isRTL?"bottom-end":"bottom-start",PLACEMENT_BOTTOMEND=isRTL?"bottom-start":"bottom-end",PLACEMENT_RIGHT=isRTL?"left-start":"right-start",PLACEMENT_LEFT=isRTL?"right-start":"left-start",Default$2={offset:0,flip:!0,boundary:"clippingParents",reference:"toggle",display:"dynamic",popperConfig:null},DefaultType$2={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"},Dropdown=function(e){function t(t,n){var i;return(i=e.call(this,t)||this)._popper=null,i._config=i._getConfig(n),i._menu=i._getMenuElement(),i._inNavbar=i._detectNavbar(),i._addEventListeners(),i}_inheritsLoose(t,e);var n=t.prototype;return n.toggle=function(){if(!this._element.disabled&&!this._element.classList.contains(CLASS_NAME_DISABLED)){var e=this._element.classList.contains(CLASS_NAME_SHOW$1);t.clearMenus(),e||this.show()}},n.show=function(){if(!(this._element.disabled||this._element.classList.contains(CLASS_NAME_DISABLED)||this._menu.classList.contains(CLASS_NAME_SHOW$1))){var e=t.getParentFromElement(this._element),n={relatedTarget:this._element};if(!EventHandler.trigger(this._element,EVENT_SHOW$1,n).defaultPrevented){if(!this._inNavbar){if(void 0===Popper)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");var i=this._element;"parent"===this._config.reference?i=e:isElement(this._config.reference)&&(i=this._config.reference,void 0!==this._config.reference.jquery&&(i=this._config.reference[0])),this._popper=createPopper(i,this._menu,this._getPopperConfig())}var o;if("ontouchstart"in document.documentElement&&!e.closest(SELECTOR_NAVBAR_NAV))(o=[]).concat.apply(o,document.body.children).forEach((function(e){return EventHandler.on(e,"mouseover",null,noop())}));this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.toggle(CLASS_NAME_SHOW$1),this._element.classList.toggle(CLASS_NAME_SHOW$1),EventHandler.trigger(e,EVENT_SHOWN$1,n)}}},n.hide=function(){if(!this._element.disabled&&!this._element.classList.contains(CLASS_NAME_DISABLED)&&this._menu.classList.contains(CLASS_NAME_SHOW$1)){var e=t.getParentFromElement(this._element),n={relatedTarget:this._element};EventHandler.trigger(e,EVENT_HIDE$1,n).defaultPrevented||(this._popper&&this._popper.destroy(),this._menu.classList.toggle(CLASS_NAME_SHOW$1),this._element.classList.toggle(CLASS_NAME_SHOW$1),EventHandler.trigger(e,EVENT_HIDDEN$1,n))}},n.dispose=function(){e.prototype.dispose.call(this),EventHandler.off(this._element,EVENT_KEY$4),this._menu=null,this._popper&&(this._popper.destroy(),this._popper=null)},n.update=function(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()},n._addEventListeners=function(){var e=this;EventHandler.on(this._element,EVENT_CLICK,(function(t){t.preventDefault(),t.stopPropagation(),e.toggle()}))},n._getConfig=function(e){return e=_extends({},this.constructor.Default,Manipulator.getDataAttributes(this._element),e),typeCheckConfig(NAME$4,e,this.constructor.DefaultType),e},n._getMenuElement=function(){return SelectorEngine.next(this._element,SELECTOR_MENU)[0]},n._getPlacement=function(){var e=this._element.parentNode;if(e.classList.contains(CLASS_NAME_DROPEND))return PLACEMENT_RIGHT;if(e.classList.contains(CLASS_NAME_DROPSTART))return PLACEMENT_LEFT;var t="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return e.classList.contains(CLASS_NAME_DROPUP)?t?PLACEMENT_TOPEND:PLACEMENT_TOP:t?PLACEMENT_BOTTOMEND:PLACEMENT_BOTTOM},n._detectNavbar=function(){return null!==this._element.closest("."+CLASS_NAME_NAVBAR)},n._getPopperConfig=function(){var e={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{altBoundary:this._config.flip,rootBoundary:this._config.boundary}}]};return"static"===this._config.display&&(e.modifiers=[{name:"applyStyles",enabled:!1}]),_extends({},e,this._config.popperConfig)},t.dropdownInterface=function(e,n){var i=Data.getData(e,DATA_KEY$4);if(i||(i=new t(e,"object"==typeof n?n:null)),"string"==typeof n){if(void 0===i[n])throw new TypeError('No method named "'+n+'"');i[n]()}},t.jQueryInterface=function(e){return this.each((function(){t.dropdownInterface(this,e)}))},t.clearMenus=function(e){if(!e||e.button!==RIGHT_MOUSE_BUTTON&&("keyup"!==e.type||e.key===TAB_KEY))for(var n=SelectorEngine.find(SELECTOR_DATA_TOGGLE$2),i=0,o=n.length;i<o;i++){var r=t.getParentFromElement(n[i]),a=Data.getData(n[i],DATA_KEY$4),s={relatedTarget:n[i]};if(e&&"click"===e.type&&(s.clickEvent=e),a){var l=a._menu;if(n[i].classList.contains(CLASS_NAME_SHOW$1))if(!(e&&("click"===e.type&&/input|textarea/i.test(e.target.tagName)||"keyup"===e.type&&e.key===TAB_KEY)&&l.contains(e.target)))if(!EventHandler.trigger(r,EVENT_HIDE$1,s).defaultPrevented){var E;if("ontouchstart"in document.documentElement)(E=[]).concat.apply(E,document.body.children).forEach((function(e){return EventHandler.off(e,"mouseover",null,noop())}));n[i].setAttribute("aria-expanded","false"),a._popper&&a._popper.destroy(),l.classList.remove(CLASS_NAME_SHOW$1),n[i].classList.remove(CLASS_NAME_SHOW$1),EventHandler.trigger(r,EVENT_HIDDEN$1,s)}}}},t.getParentFromElement=function(e){return getElementFromSelector(e)||e.parentNode},t.dataApiKeydownHandler=function(e){if(!(/input|textarea/i.test(e.target.tagName)?e.key===SPACE_KEY||e.key!==ESCAPE_KEY&&(e.key!==ARROW_DOWN_KEY&&e.key!==ARROW_UP_KEY||e.target.closest(SELECTOR_MENU)):!REGEXP_KEYDOWN.test(e.key))&&(e.preventDefault(),e.stopPropagation(),!this.disabled&&!this.classList.contains(CLASS_NAME_DISABLED))){var n=t.getParentFromElement(this),i=this.classList.contains(CLASS_NAME_SHOW$1);if(e.key===ESCAPE_KEY)return(this.matches(SELECTOR_DATA_TOGGLE$2)?this:SelectorEngine.prev(this,SELECTOR_DATA_TOGGLE$2)[0]).focus(),void t.clearMenus();if(i&&e.key!==SPACE_KEY){var o=SelectorEngine.find(SELECTOR_VISIBLE_ITEMS,n).filter(isVisible);if(o.length){var r=o.indexOf(e.target);e.key===ARROW_UP_KEY&&r>0&&r--,e.key===ARROW_DOWN_KEY&&r<o.length-1&&r++,o[r=-1===r?0:r].focus()}}else t.clearMenus()}},_createClass(t,null,[{key:"Default",get:function(){return Default$2}},{key:"DefaultType",get:function(){return DefaultType$2}},{key:"DATA_KEY",get:function(){return DATA_KEY$4}}]),t}(BaseComponent);EventHandler.on(document,EVENT_KEYDOWN_DATA_API,SELECTOR_DATA_TOGGLE$2,Dropdown.dataApiKeydownHandler),EventHandler.on(document,EVENT_KEYDOWN_DATA_API,SELECTOR_MENU,Dropdown.dataApiKeydownHandler),EventHandler.on(document,EVENT_CLICK_DATA_API$4,Dropdown.clearMenus),EventHandler.on(document,EVENT_KEYUP_DATA_API,Dropdown.clearMenus),EventHandler.on(document,EVENT_CLICK_DATA_API$4,SELECTOR_DATA_TOGGLE$2,(function(e){e.preventDefault(),e.stopPropagation(),Dropdown.dropdownInterface(this,"toggle")})),EventHandler.on(document,EVENT_CLICK_DATA_API$4,SELECTOR_FORM_CHILD,(function(e){return e.stopPropagation()})),onDOMContentLoaded((function(){var e=getjQuery();if(e){var t=e.fn[NAME$4];e.fn[NAME$4]=Dropdown.jQueryInterface,e.fn[NAME$4].Constructor=Dropdown,e.fn[NAME$4].noConflict=function(){return e.fn[NAME$4]=t,Dropdown.jQueryInterface}}}));var NAME$5="modal",DATA_KEY$5="bs.modal",EVENT_KEY$5="."+DATA_KEY$5,DATA_API_KEY$5=".data-api",ESCAPE_KEY$1="Escape",Default$3={backdrop:!0,keyboard:!0,focus:!0},DefaultType$3={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean"},EVENT_HIDE$2="hide"+EVENT_KEY$5,EVENT_HIDE_PREVENTED="hidePrevented"+EVENT_KEY$5,EVENT_HIDDEN$2="hidden"+EVENT_KEY$5,EVENT_SHOW$2="show"+EVENT_KEY$5,EVENT_SHOWN$2="shown"+EVENT_KEY$5,EVENT_FOCUSIN="focusin"+EVENT_KEY$5,EVENT_RESIZE="resize"+EVENT_KEY$5,EVENT_CLICK_DISMISS="click.dismiss"+EVENT_KEY$5,EVENT_KEYDOWN_DISMISS="keydown.dismiss"+EVENT_KEY$5,EVENT_MOUSEUP_DISMISS="mouseup.dismiss"+EVENT_KEY$5,EVENT_MOUSEDOWN_DISMISS="mousedown.dismiss"+EVENT_KEY$5,EVENT_CLICK_DATA_API$5="click"+EVENT_KEY$5+DATA_API_KEY$5,CLASS_NAME_SCROLLBAR_MEASURER="modal-scrollbar-measure",CLASS_NAME_BACKDROP="modal-backdrop",CLASS_NAME_OPEN="modal-open",CLASS_NAME_FADE="fade",CLASS_NAME_SHOW$2="show",CLASS_NAME_STATIC="modal-static",SELECTOR_DIALOG=".modal-dialog",SELECTOR_MODAL_BODY=".modal-body",SELECTOR_DATA_TOGGLE$3='[data-bs-toggle="modal"]',SELECTOR_DATA_DISMISS='[data-bs-dismiss="modal"]',SELECTOR_FIXED_CONTENT=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",SELECTOR_STICKY_CONTENT=".sticky-top",Modal=function(e){function t(t,n){var i;return(i=e.call(this,t)||this)._config=i._getConfig(n),i._dialog=SelectorEngine.findOne(SELECTOR_DIALOG,t),i._backdrop=null,i._isShown=!1,i._isBodyOverflowing=!1,i._ignoreBackdropClick=!1,i._isTransitioning=!1,i._scrollbarWidth=0,i}_inheritsLoose(t,e);var n=t.prototype;return n.toggle=function(e){return this._isShown?this.hide():this.show(e)},n.show=function(e){var t=this;if(!this._isShown&&!this._isTransitioning){this._element.classList.contains(CLASS_NAME_FADE)&&(this._isTransitioning=!0);var n=EventHandler.trigger(this._element,EVENT_SHOW$2,{relatedTarget:e});this._isShown||n.defaultPrevented||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),EventHandler.on(this._element,EVENT_CLICK_DISMISS,SELECTOR_DATA_DISMISS,(function(e){return t.hide(e)})),EventHandler.on(this._dialog,EVENT_MOUSEDOWN_DISMISS,(function(){EventHandler.one(t._element,EVENT_MOUSEUP_DISMISS,(function(e){e.target===t._element&&(t._ignoreBackdropClick=!0)}))})),this._showBackdrop((function(){return t._showElement(e)})))}},n.hide=function(e){var t=this;if((e&&e.preventDefault(),this._isShown&&!this._isTransitioning)&&!EventHandler.trigger(this._element,EVENT_HIDE$2).defaultPrevented){this._isShown=!1;var n=this._element.classList.contains(CLASS_NAME_FADE);if(n&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),EventHandler.off(document,EVENT_FOCUSIN),this._element.classList.remove(CLASS_NAME_SHOW$2),EventHandler.off(this._element,EVENT_CLICK_DISMISS),EventHandler.off(this._dialog,EVENT_MOUSEDOWN_DISMISS),n){var i=getTransitionDurationFromElement(this._element);EventHandler.one(this._element,TRANSITION_END,(function(e){return t._hideModal(e)})),emulateTransitionEnd(this._element,i)}else this._hideModal()}},n.dispose=function(){[window,this._element,this._dialog].forEach((function(e){return EventHandler.off(e,EVENT_KEY$5)})),e.prototype.dispose.call(this),EventHandler.off(document,EVENT_FOCUSIN),this._config=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},n.handleUpdate=function(){this._adjustDialog()},n._getConfig=function(e){return e=_extends({},Default$3,e),typeCheckConfig(NAME$5,e,DefaultType$3),e},n._showElement=function(e){var t=this,n=this._element.classList.contains(CLASS_NAME_FADE),i=SelectorEngine.findOne(SELECTOR_MODAL_BODY,this._dialog);this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0,i&&(i.scrollTop=0),n&&reflow(this._element),this._element.classList.add(CLASS_NAME_SHOW$2),this._config.focus&&this._enforceFocus();var o=function(){t._config.focus&&t._element.focus(),t._isTransitioning=!1,EventHandler.trigger(t._element,EVENT_SHOWN$2,{relatedTarget:e})};if(n){var r=getTransitionDurationFromElement(this._dialog);EventHandler.one(this._dialog,TRANSITION_END,o),emulateTransitionEnd(this._dialog,r)}else o()},n._enforceFocus=function(){var e=this;EventHandler.off(document,EVENT_FOCUSIN),EventHandler.on(document,EVENT_FOCUSIN,(function(t){document===t.target||e._element===t.target||e._element.contains(t.target)||e._element.focus()}))},n._setEscapeEvent=function(){var e=this;this._isShown?EventHandler.on(this._element,EVENT_KEYDOWN_DISMISS,(function(t){e._config.keyboard&&t.key===ESCAPE_KEY$1?(t.preventDefault(),e.hide()):e._config.keyboard||t.key!==ESCAPE_KEY$1||e._triggerBackdropTransition()})):EventHandler.off(this._element,EVENT_KEYDOWN_DISMISS)},n._setResizeEvent=function(){var e=this;this._isShown?EventHandler.on(window,EVENT_RESIZE,(function(){return e._adjustDialog()})):EventHandler.off(window,EVENT_RESIZE)},n._hideModal=function(){var e=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._showBackdrop((function(){document.body.classList.remove(CLASS_NAME_OPEN),e._resetAdjustments(),e._resetScrollbar(),EventHandler.trigger(e._element,EVENT_HIDDEN$2)}))},n._removeBackdrop=function(){this._backdrop.parentNode.removeChild(this._backdrop),this._backdrop=null},n._showBackdrop=function(e){var t=this,n=this._element.classList.contains(CLASS_NAME_FADE)?CLASS_NAME_FADE:"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className=CLASS_NAME_BACKDROP,n&&this._backdrop.classList.add(n),document.body.appendChild(this._backdrop),EventHandler.on(this._element,EVENT_CLICK_DISMISS,(function(e){t._ignoreBackdropClick?t._ignoreBackdropClick=!1:e.target===e.currentTarget&&("static"===t._config.backdrop?t._triggerBackdropTransition():t.hide())})),n&&reflow(this._backdrop),this._backdrop.classList.add(CLASS_NAME_SHOW$2),!n)return void e();var i=getTransitionDurationFromElement(this._backdrop);EventHandler.one(this._backdrop,TRANSITION_END,e),emulateTransitionEnd(this._backdrop,i)}else if(!this._isShown&&this._backdrop){this._backdrop.classList.remove(CLASS_NAME_SHOW$2);var o=function(){t._removeBackdrop(),e()};if(this._element.classList.contains(CLASS_NAME_FADE)){var r=getTransitionDurationFromElement(this._backdrop);EventHandler.one(this._backdrop,TRANSITION_END,o),emulateTransitionEnd(this._backdrop,r)}else o()}else e()},n._triggerBackdropTransition=function(){var e=this;if(!EventHandler.trigger(this._element,EVENT_HIDE_PREVENTED).defaultPrevented){var t=this._element.scrollHeight>document.documentElement.clientHeight;t||(this._element.style.overflowY="hidden"),this._element.classList.add(CLASS_NAME_STATIC);var n=getTransitionDurationFromElement(this._dialog);EventHandler.off(this._element,TRANSITION_END),EventHandler.one(this._element,TRANSITION_END,(function(){e._element.classList.remove(CLASS_NAME_STATIC),t||(EventHandler.one(e._element,TRANSITION_END,(function(){e._element.style.overflowY=""})),emulateTransitionEnd(e._element,n))})),emulateTransitionEnd(this._element,n),this._element.focus()}},n._adjustDialog=function(){var e=this._element.scrollHeight>document.documentElement.clientHeight;(!this._isBodyOverflowing&&e&&!isRTL||this._isBodyOverflowing&&!e&&isRTL)&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),(this._isBodyOverflowing&&!e&&!isRTL||!this._isBodyOverflowing&&e&&isRTL)&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},n._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},n._checkScrollbar=function(){var e=document.body.getBoundingClientRect();this._isBodyOverflowing=Math.round(e.left+e.right)<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},n._setScrollbar=function(){var e=this;if(this._isBodyOverflowing){SelectorEngine.find(SELECTOR_FIXED_CONTENT).forEach((function(t){var n=t.style.paddingRight,i=window.getComputedStyle(t)["padding-right"];Manipulator.setDataAttribute(t,"padding-right",n),t.style.paddingRight=Number.parseFloat(i)+e._scrollbarWidth+"px"})),SelectorEngine.find(SELECTOR_STICKY_CONTENT).forEach((function(t){var n=t.style.marginRight,i=window.getComputedStyle(t)["margin-right"];Manipulator.setDataAttribute(t,"margin-right",n),t.style.marginRight=Number.parseFloat(i)-e._scrollbarWidth+"px"}));var t=document.body.style.paddingRight,n=window.getComputedStyle(document.body)["padding-right"];Manipulator.setDataAttribute(document.body,"padding-right",t),document.body.style.paddingRight=Number.parseFloat(n)+this._scrollbarWidth+"px"}document.body.classList.add(CLASS_NAME_OPEN)},n._resetScrollbar=function(){SelectorEngine.find(SELECTOR_FIXED_CONTENT).forEach((function(e){var t=Manipulator.getDataAttribute(e,"padding-right");void 0!==t&&(Manipulator.removeDataAttribute(e,"padding-right"),e.style.paddingRight=t)})),SelectorEngine.find(""+SELECTOR_STICKY_CONTENT).forEach((function(e){var t=Manipulator.getDataAttribute(e,"margin-right");void 0!==t&&(Manipulator.removeDataAttribute(e,"margin-right"),e.style.marginRight=t)}));var e=Manipulator.getDataAttribute(document.body,"padding-right");void 0===e?document.body.style.paddingRight="":(Manipulator.removeDataAttribute(document.body,"padding-right"),document.body.style.paddingRight=e)},n._getScrollbarWidth=function(){var e=document.createElement("div");e.className=CLASS_NAME_SCROLLBAR_MEASURER,document.body.appendChild(e);var t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t},t.jQueryInterface=function(e,n){return this.each((function(){var i=Data.getData(this,DATA_KEY$5),o=_extends({},Default$3,Manipulator.getDataAttributes(this),"object"==typeof e&&e?e:{});if(i||(i=new t(this,o)),"string"==typeof e){if(void 0===i[e])throw new TypeError('No method named "'+e+'"');i[e](n)}}))},_createClass(t,null,[{key:"Default",get:function(){return Default$3}},{key:"DATA_KEY",get:function(){return DATA_KEY$5}}]),t}(BaseComponent);EventHandler.on(document,EVENT_CLICK_DATA_API$5,SELECTOR_DATA_TOGGLE$3,(function(e){var t=this,n=getElementFromSelector(this);"A"!==this.tagName&&"AREA"!==this.tagName||e.preventDefault(),EventHandler.one(n,EVENT_SHOW$2,(function(e){e.defaultPrevented||EventHandler.one(n,EVENT_HIDDEN$2,(function(){isVisible(t)&&t.focus()}))}));var i=Data.getData(n,DATA_KEY$5);if(!i){var o=_extends({},Manipulator.getDataAttributes(n),Manipulator.getDataAttributes(this));i=new Modal(n,o)}i.show(this)})),onDOMContentLoaded((function(){var e=getjQuery();if(e){var t=e.fn[NAME$5];e.fn[NAME$5]=Modal.jQueryInterface,e.fn[NAME$5].Constructor=Modal,e.fn[NAME$5].noConflict=function(){return e.fn[NAME$5]=t,Modal.jQueryInterface}}}));var uriAttrs=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),ARIA_ATTRIBUTE_PATTERN=/^aria-[\w-]*$/i,SAFE_URL_PATTERN=/^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi,DATA_URL_PATTERN=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i,allowedAttribute=function(e,t){var n=e.nodeName.toLowerCase();if(t.includes(n))return!uriAttrs.has(n)||Boolean(e.nodeValue.match(SAFE_URL_PATTERN)||e.nodeValue.match(DATA_URL_PATTERN));for(var i=t.filter((function(e){return e instanceof RegExp})),o=0,r=i.length;o<r;o++)if(n.match(i[o]))return!0;return!1},DefaultAllowlist={"*":["class","dir","id","lang","role",ARIA_ATTRIBUTE_PATTERN],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]};function sanitizeHtml(e,t,n){var i;if(!e.length)return e;if(n&&"function"==typeof n)return n(e);for(var o=(new window.DOMParser).parseFromString(e,"text/html"),r=Object.keys(t),a=(i=[]).concat.apply(i,o.body.querySelectorAll("*")),s=function(e,n){var i,o=a[e],s=o.nodeName.toLowerCase();if(!r.includes(s))return o.parentNode.removeChild(o),"continue";var l=(i=[]).concat.apply(i,o.attributes),E=[].concat(t["*"]||[],t[s]||[]);l.forEach((function(e){allowedAttribute(e,E)||o.removeAttribute(e.nodeName)}))},l=0,E=a.length;l<E;l++)s(l);return o.body.innerHTML}var NAME$6="tooltip",DATA_KEY$6="bs.tooltip",EVENT_KEY$6="."+DATA_KEY$6,CLASS_PREFIX="bs-tooltip",BSCLS_PREFIX_REGEX=new RegExp("(^|\\s)"+CLASS_PREFIX+"\\S+","g"),DISALLOWED_ATTRIBUTES=new Set(["sanitize","allowList","sanitizeFn"]),DefaultType$4={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",container:"(string|element|boolean)",fallbackPlacements:"(null|array)",boundary:"(string|element)",customClass:"(string|function)",sanitize:"boolean",sanitizeFn:"(null|function)",allowList:"object",popperConfig:"(null|object)"},AttachmentMap={AUTO:"auto",TOP:"top",RIGHT:isRTL?"left":"right",BOTTOM:"bottom",LEFT:isRTL?"right":"left"},Default$4={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",container:!1,fallbackPlacements:null,boundary:"clippingParents",customClass:"",sanitize:!0,sanitizeFn:null,allowList:DefaultAllowlist,popperConfig:null},Event$1={HIDE:"hide"+EVENT_KEY$6,HIDDEN:"hidden"+EVENT_KEY$6,SHOW:"show"+EVENT_KEY$6,SHOWN:"shown"+EVENT_KEY$6,INSERTED:"inserted"+EVENT_KEY$6,CLICK:"click"+EVENT_KEY$6,FOCUSIN:"focusin"+EVENT_KEY$6,FOCUSOUT:"focusout"+EVENT_KEY$6,MOUSEENTER:"mouseenter"+EVENT_KEY$6,MOUSELEAVE:"mouseleave"+EVENT_KEY$6},CLASS_NAME_FADE$1="fade",CLASS_NAME_MODAL="modal",CLASS_NAME_SHOW$3="show",HOVER_STATE_SHOW="show",HOVER_STATE_OUT="out",SELECTOR_TOOLTIP_INNER=".tooltip-inner",TRIGGER_HOVER="hover",TRIGGER_FOCUS="focus",TRIGGER_CLICK="click",TRIGGER_MANUAL="manual",Tooltip=function(e){function t(t,n){var i;if(void 0===Popper)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");return(i=e.call(this,t)||this)._isEnabled=!0,i._timeout=0,i._hoverState="",i._activeTrigger={},i._popper=null,i.config=i._getConfig(n),i.tip=null,i._setListeners(),i}_inheritsLoose(t,e);var n=t.prototype;return n.enable=function(){this._isEnabled=!0},n.disable=function(){this._isEnabled=!1},n.toggleEnabled=function(){this._isEnabled=!this._isEnabled},n.toggle=function(e){if(this._isEnabled)if(e){var t=this.constructor.DATA_KEY,n=Data.getData(e.delegateTarget,t);n||(n=new this.constructor(e.delegateTarget,this._getDelegateConfig()),Data.setData(e.delegateTarget,t,n)),n._activeTrigger.click=!n._activeTrigger.click,n._isWithActiveTrigger()?n._enter(null,n):n._leave(null,n)}else{if(this.getTipElement().classList.contains(CLASS_NAME_SHOW$3))return void this._leave(null,this);this._enter(null,this)}},n.dispose=function(){clearTimeout(this._timeout),EventHandler.off(this._element,this.constructor.EVENT_KEY),EventHandler.off(this._element.closest("."+CLASS_NAME_MODAL),"hide.bs.modal",this._hideModalHandler),this.tip&&this.tip.parentNode.removeChild(this.tip),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.config=null,this.tip=null,e.prototype.dispose.call(this)},n.show=function(){var e=this;if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(this.isWithContent()&&this._isEnabled){var t=EventHandler.trigger(this._element,this.constructor.Event.SHOW),n=findShadowRoot(this._element),i=null===n?this._element.ownerDocument.documentElement.contains(this._element):n.contains(this._element);if(t.defaultPrevented||!i)return;var o=this.getTipElement(),r=getUID(this.constructor.NAME);o.setAttribute("id",r),this._element.setAttribute("aria-describedby",r),this.setContent(),this.config.animation&&o.classList.add(CLASS_NAME_FADE$1);var a="function"==typeof this.config.placement?this.config.placement.call(this,o,this._element):this.config.placement,s=this._getAttachment(a);this._addAttachmentClass(s);var l=this._getContainer();Data.setData(o,this.constructor.DATA_KEY,this),this._element.ownerDocument.documentElement.contains(this.tip)||l.appendChild(o),EventHandler.trigger(this._element,this.constructor.Event.INSERTED),this._popper=createPopper(this._element,o,this._getPopperConfig(s)),o.classList.add(CLASS_NAME_SHOW$3);var E,_,c="function"==typeof this.config.customClass?this.config.customClass():this.config.customClass;if(c)(E=o.classList).add.apply(E,c.split(" "));if("ontouchstart"in document.documentElement)(_=[]).concat.apply(_,document.body.children).forEach((function(e){EventHandler.on(e,"mouseover",noop())}));var u=function(){var t=e._hoverState;e._hoverState=null,EventHandler.trigger(e._element,e.constructor.Event.SHOWN),t===HOVER_STATE_OUT&&e._leave(null,e)};if(this.tip.classList.contains(CLASS_NAME_FADE$1)){var d=getTransitionDurationFromElement(this.tip);EventHandler.one(this.tip,TRANSITION_END,u),emulateTransitionEnd(this.tip,d)}else u()}},n.hide=function(){var e=this;if(this._popper){var t=this.getTipElement(),n=function(){e._hoverState!==HOVER_STATE_SHOW&&t.parentNode&&t.parentNode.removeChild(t),e._cleanTipClass(),e._element.removeAttribute("aria-describedby"),EventHandler.trigger(e._element,e.constructor.Event.HIDDEN),e._popper&&(e._popper.destroy(),e._popper=null)};if(!EventHandler.trigger(this._element,this.constructor.Event.HIDE).defaultPrevented){var i;if(t.classList.remove(CLASS_NAME_SHOW$3),"ontouchstart"in document.documentElement)(i=[]).concat.apply(i,document.body.children).forEach((function(e){return EventHandler.off(e,"mouseover",noop)}));if(this._activeTrigger[TRIGGER_CLICK]=!1,this._activeTrigger[TRIGGER_FOCUS]=!1,this._activeTrigger[TRIGGER_HOVER]=!1,this.tip.classList.contains(CLASS_NAME_FADE$1)){var o=getTransitionDurationFromElement(t);EventHandler.one(t,TRANSITION_END,n),emulateTransitionEnd(t,o)}else n();this._hoverState=""}}},n.update=function(){null!==this._popper&&this._popper.update()},n.isWithContent=function(){return Boolean(this.getTitle())},n.getTipElement=function(){if(this.tip)return this.tip;var e=document.createElement("div");return e.innerHTML=this.config.template,this.tip=e.children[0],this.tip},n.setContent=function(){var e=this.getTipElement();this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER,e),this.getTitle()),e.classList.remove(CLASS_NAME_FADE$1,CLASS_NAME_SHOW$3)},n.setElementContent=function(e,t){if(null!==e)return"object"==typeof t&&isElement(t)?(t.jquery&&(t=t[0]),void(this.config.html?t.parentNode!==e&&(e.innerHTML="",e.appendChild(t)):e.textContent=t.textContent)):void(this.config.html?(this.config.sanitize&&(t=sanitizeHtml(t,this.config.allowList,this.config.sanitizeFn)),e.innerHTML=t):e.textContent=t)},n.getTitle=function(){var e=this._element.getAttribute("data-bs-original-title");return e||(e="function"==typeof this.config.title?this.config.title.call(this._element):this.config.title),e},n.updateAttachment=function(e){return"right"===e?"end":"left"===e?"start":e},n._getPopperConfig=function(e){var t=this,n={name:"flip",options:{altBoundary:!0}};return this.config.fallbackPlacements&&(n.options.fallbackPlacements=this.config.fallbackPlacements),_extends({},{placement:e,modifiers:[n,{name:"preventOverflow",options:{rootBoundary:this.config.boundary}},{name:"arrow",options:{element:"."+this.constructor.NAME+"-arrow"}},{name:"onChange",enabled:!0,phase:"afterWrite",fn:function(e){return t._handlePopperPlacementChange(e)}}],onFirstUpdate:function(e){e.options.placement!==e.placement&&t._handlePopperPlacementChange(e)}},this.config.popperConfig)},n._addAttachmentClass=function(e){this.getTipElement().classList.add(CLASS_PREFIX+"-"+this.updateAttachment(e))},n._getContainer=function(){return!1===this.config.container?document.body:isElement(this.config.container)?this.config.container:SelectorEngine.findOne(this.config.container)},n._getAttachment=function(e){return AttachmentMap[e.toUpperCase()]},n._setListeners=function(){var e=this;this.config.trigger.split(" ").forEach((function(t){if("click"===t)EventHandler.on(e._element,e.constructor.Event.CLICK,e.config.selector,(function(t){return e.toggle(t)}));else if(t!==TRIGGER_MANUAL){var n=t===TRIGGER_HOVER?e.constructor.Event.MOUSEENTER:e.constructor.Event.FOCUSIN,i=t===TRIGGER_HOVER?e.constructor.Event.MOUSELEAVE:e.constructor.Event.FOCUSOUT;EventHandler.on(e._element,n,e.config.selector,(function(t){return e._enter(t)})),EventHandler.on(e._element,i,e.config.selector,(function(t){return e._leave(t)}))}})),this._hideModalHandler=function(){e._element&&e.hide()},EventHandler.on(this._element.closest("."+CLASS_NAME_MODAL),"hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=_extends({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},n._fixTitle=function(){var e=this._element.getAttribute("title"),t=typeof this._element.getAttribute("data-bs-original-title");(e||"string"!==t)&&(this._element.setAttribute("data-bs-original-title",e||""),!e||this._element.getAttribute("aria-label")||this._element.textContent||this._element.setAttribute("aria-label",e),this._element.setAttribute("title",""))},n._enter=function(e,t){var n=this.constructor.DATA_KEY;(t=t||Data.getData(e.delegateTarget,n))||(t=new this.constructor(e.delegateTarget,this._getDelegateConfig()),Data.setData(e.delegateTarget,n,t)),e&&(t._activeTrigger["focusin"===e.type?TRIGGER_FOCUS:TRIGGER_HOVER]=!0),t.getTipElement().classList.contains(CLASS_NAME_SHOW$3)||t._hoverState===HOVER_STATE_SHOW?t._hoverState=HOVER_STATE_SHOW:(clearTimeout(t._timeout),t._hoverState=HOVER_STATE_SHOW,t.config.delay&&t.config.delay.show?t._timeout=setTimeout((function(){t._hoverState===HOVER_STATE_SHOW&&t.show()}),t.config.delay.show):t.show())},n._leave=function(e,t){var n=this.constructor.DATA_KEY;(t=t||Data.getData(e.delegateTarget,n))||(t=new this.constructor(e.delegateTarget,this._getDelegateConfig()),Data.setData(e.delegateTarget,n,t)),e&&(t._activeTrigger["focusout"===e.type?TRIGGER_FOCUS:TRIGGER_HOVER]=!1),t._isWithActiveTrigger()||(clearTimeout(t._timeout),t._hoverState=HOVER_STATE_OUT,t.config.delay&&t.config.delay.hide?t._timeout=setTimeout((function(){t._hoverState===HOVER_STATE_OUT&&t.hide()}),t.config.delay.hide):t.hide())},n._isWithActiveTrigger=function(){for(var e in this._activeTrigger)if(this._activeTrigger[e])return!0;return!1},n._getConfig=function(e){var t=Manipulator.getDataAttributes(this._element);return Object.keys(t).forEach((function(e){DISALLOWED_ATTRIBUTES.has(e)&&delete t[e]})),e&&"object"==typeof e.container&&e.container.jquery&&(e.container=e.container[0]),"number"==typeof(e=_extends({},this.constructor.Default,t,"object"==typeof e&&e?e:{})).delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),typeCheckConfig(NAME$6,e,this.constructor.DefaultType),e.sanitize&&(e.template=sanitizeHtml(e.template,e.allowList,e.sanitizeFn)),e},n._getDelegateConfig=function(){var e={};if(this.config)for(var t in this.config)this.constructor.Default[t]!==this.config[t]&&(e[t]=this.config[t]);return e},n._cleanTipClass=function(){var e=this.getTipElement(),t=e.getAttribute("class").match(BSCLS_PREFIX_REGEX);null!==t&&t.length>0&&t.map((function(e){return e.trim()})).forEach((function(t){return e.classList.remove(t)}))},n._handlePopperPlacementChange=function(e){var t=e.state;t&&(this.tip=t.elements.popper,this._cleanTipClass(),this._addAttachmentClass(this._getAttachment(t.placement)))},t.jQueryInterface=function(e){return this.each((function(){var n=Data.getData(this,DATA_KEY$6),i="object"==typeof e&&e;if((n||!/dispose|hide/.test(e))&&(n||(n=new t(this,i)),"string"==typeof e)){if(void 0===n[e])throw new TypeError('No method named "'+e+'"');n[e]()}}))},_createClass(t,null,[{key:"Default",get:function(){return Default$4}},{key:"NAME",get:function(){return NAME$6}},{key:"DATA_KEY",get:function(){return DATA_KEY$6}},{key:"Event",get:function(){return Event$1}},{key:"EVENT_KEY",get:function(){return EVENT_KEY$6}},{key:"DefaultType",get:function(){return DefaultType$4}}]),t}(BaseComponent);onDOMContentLoaded((function(){var e=getjQuery();if(e){var t=e.fn[NAME$6];e.fn[NAME$6]=Tooltip.jQueryInterface,e.fn[NAME$6].Constructor=Tooltip,e.fn[NAME$6].noConflict=function(){return e.fn[NAME$6]=t,Tooltip.jQueryInterface}}}));var NAME$7="popover",DATA_KEY$7="bs.popover",EVENT_KEY$7="."+DATA_KEY$7,CLASS_PREFIX$1="bs-popover",BSCLS_PREFIX_REGEX$1=new RegExp("(^|\\s)"+CLASS_PREFIX$1+"\\S+","g"),Default$5=_extends({},Tooltip.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),DefaultType$5=_extends({},Tooltip.DefaultType,{content:"(string|element|function)"}),Event$2={HIDE:"hide"+EVENT_KEY$7,HIDDEN:"hidden"+EVENT_KEY$7,SHOW:"show"+EVENT_KEY$7,SHOWN:"shown"+EVENT_KEY$7,INSERTED:"inserted"+EVENT_KEY$7,CLICK:"click"+EVENT_KEY$7,FOCUSIN:"focusin"+EVENT_KEY$7,FOCUSOUT:"focusout"+EVENT_KEY$7,MOUSEENTER:"mouseenter"+EVENT_KEY$7,MOUSELEAVE:"mouseleave"+EVENT_KEY$7},CLASS_NAME_FADE$2="fade",CLASS_NAME_SHOW$4="show",SELECTOR_TITLE=".popover-header",SELECTOR_CONTENT=".popover-body",Popover=function(e){function t(){return e.apply(this,arguments)||this}_inheritsLoose(t,e);var n=t.prototype;return n.isWithContent=function(){return this.getTitle()||this._getContent()},n.setContent=function(){var e=this.getTipElement();this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE,e),this.getTitle());var t=this._getContent();"function"==typeof t&&(t=t.call(this._element)),this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT,e),t),e.classList.remove(CLASS_NAME_FADE$2,CLASS_NAME_SHOW$4)},n._addAttachmentClass=function(e){this.getTipElement().classList.add(CLASS_PREFIX$1+"-"+this.updateAttachment(e))},n._getContent=function(){return this._element.getAttribute("data-bs-content")||this.config.content},n._cleanTipClass=function(){var e=this.getTipElement(),t=e.getAttribute("class").match(BSCLS_PREFIX_REGEX$1);null!==t&&t.length>0&&t.map((function(e){return e.trim()})).forEach((function(t){return e.classList.remove(t)}))},t.jQueryInterface=function(e){return this.each((function(){var n=Data.getData(this,DATA_KEY$7),i="object"==typeof e?e:null;if((n||!/dispose|hide/.test(e))&&(n||(n=new t(this,i),Data.setData(this,DATA_KEY$7,n)),"string"==typeof e)){if(void 0===n[e])throw new TypeError('No method named "'+e+'"');n[e]()}}))},_createClass(t,null,[{key:"Default",get:function(){return Default$5}},{key:"NAME",get:function(){return NAME$7}},{key:"DATA_KEY",get:function(){return DATA_KEY$7}},{key:"Event",get:function(){return Event$2}},{key:"EVENT_KEY",get:function(){return EVENT_KEY$7}},{key:"DefaultType",get:function(){return DefaultType$5}}]),t}(Tooltip);onDOMContentLoaded((function(){var e=getjQuery();if(e){var t=e.fn[NAME$7];e.fn[NAME$7]=Popover.jQueryInterface,e.fn[NAME$7].Constructor=Popover,e.fn[NAME$7].noConflict=function(){return e.fn[NAME$7]=t,Popover.jQueryInterface}}}));var NAME$8="scrollspy",DATA_KEY$8="bs.scrollspy",EVENT_KEY$8="."+DATA_KEY$8,DATA_API_KEY$6=".data-api",Default$6={offset:10,method:"auto",target:""},DefaultType$6={offset:"number",method:"string",target:"(string|element)"},EVENT_ACTIVATE="activate"+EVENT_KEY$8,EVENT_SCROLL="scroll"+EVENT_KEY$8,EVENT_LOAD_DATA_API$1="load"+EVENT_KEY$8+DATA_API_KEY$6,CLASS_NAME_DROPDOWN_ITEM="dropdown-item",CLASS_NAME_ACTIVE$2="active",SELECTOR_DATA_SPY='[data-bs-spy="scroll"]',SELECTOR_NAV_LIST_GROUP=".nav, .list-group",SELECTOR_NAV_LINKS=".nav-link",SELECTOR_NAV_ITEMS=".nav-item",SELECTOR_LIST_ITEMS=".list-group-item",SELECTOR_DROPDOWN=".dropdown",SELECTOR_DROPDOWN_TOGGLE=".dropdown-toggle",METHOD_OFFSET="offset",METHOD_POSITION="position",ScrollSpy=function(e){function t(t,n){var i;return(i=e.call(this,t)||this)._scrollElement="BODY"===t.tagName?window:t,i._config=i._getConfig(n),i._selector=i._config.target+" "+SELECTOR_NAV_LINKS+", "+i._config.target+" "+SELECTOR_LIST_ITEMS+", "+i._config.target+" ."+CLASS_NAME_DROPDOWN_ITEM,i._offsets=[],i._targets=[],i._activeTarget=null,i._scrollHeight=0,EventHandler.on(i._scrollElement,EVENT_SCROLL,(function(e){return i._process(e)})),i.refresh(),i._process(),i}_inheritsLoose(t,e);var n=t.prototype;return n.refresh=function(){var e=this,t=this._scrollElement===this._scrollElement.window?METHOD_OFFSET:METHOD_POSITION,n="auto"===this._config.method?t:this._config.method,i=n===METHOD_POSITION?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),SelectorEngine.find(this._selector).map((function(e){var t=getSelectorFromElement(e),o=t?SelectorEngine.findOne(t):null;if(o){var r=o.getBoundingClientRect();if(r.width||r.height)return[Manipulator[n](o).top+i,t]}return null})).filter((function(e){return e})).sort((function(e,t){return e[0]-t[0]})).forEach((function(t){e._offsets.push(t[0]),e._targets.push(t[1])}))},n.dispose=function(){e.prototype.dispose.call(this),EventHandler.off(this._scrollElement,EVENT_KEY$8),this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},n._getConfig=function(e){if("string"!=typeof(e=_extends({},Default$6,"object"==typeof e&&e?e:{})).target&&isElement(e.target)){var t=e.target.id;t||(t=getUID(NAME$8),e.target.id=t),e.target="#"+t}return typeCheckConfig(NAME$8,e,DefaultType$6),e},n._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},n._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},n._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},n._process=function(){var e=this._getScrollTop()+this._config.offset,t=this._getScrollHeight(),n=this._config.offset+t-this._getOffsetHeight();if(this._scrollHeight!==t&&this.refresh(),e>=n){var i=this._targets[this._targets.length-1];this._activeTarget!==i&&this._activate(i)}else{if(this._activeTarget&&e<this._offsets[0]&&this._offsets[0]>0)return this._activeTarget=null,void this._clear();for(var o=this._offsets.length;o--;){this._activeTarget!==this._targets[o]&&e>=this._offsets[o]&&(void 0===this._offsets[o+1]||e<this._offsets[o+1])&&this._activate(this._targets[o])}}},n._activate=function(e){this._activeTarget=e,this._clear();var t=this._selector.split(",").map((function(t){return t+'[data-bs-target="'+e+'"],'+t+'[href="'+e+'"]'})),n=SelectorEngine.findOne(t.join(","));n.classList.contains(CLASS_NAME_DROPDOWN_ITEM)?(SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE,n.closest(SELECTOR_DROPDOWN)).classList.add(CLASS_NAME_ACTIVE$2),n.classList.add(CLASS_NAME_ACTIVE$2)):(n.classList.add(CLASS_NAME_ACTIVE$2),SelectorEngine.parents(n,SELECTOR_NAV_LIST_GROUP).forEach((function(e){SelectorEngine.prev(e,SELECTOR_NAV_LINKS+", "+SELECTOR_LIST_ITEMS).forEach((function(e){return e.classList.add(CLASS_NAME_ACTIVE$2)})),SelectorEngine.prev(e,SELECTOR_NAV_ITEMS).forEach((function(e){SelectorEngine.children(e,SELECTOR_NAV_LINKS).forEach((function(e){return e.classList.add(CLASS_NAME_ACTIVE$2)}))}))}))),EventHandler.trigger(this._scrollElement,EVENT_ACTIVATE,{relatedTarget:e})},n._clear=function(){SelectorEngine.find(this._selector).filter((function(e){return e.classList.contains(CLASS_NAME_ACTIVE$2)})).forEach((function(e){return e.classList.remove(CLASS_NAME_ACTIVE$2)}))},t.jQueryInterface=function(e){return this.each((function(){var n=Data.getData(this,DATA_KEY$8);if(n||(n=new t(this,"object"==typeof e&&e)),"string"==typeof e){if(void 0===n[e])throw new TypeError('No method named "'+e+'"');n[e]()}}))},_createClass(t,null,[{key:"Default",get:function(){return Default$6}},{key:"DATA_KEY",get:function(){return DATA_KEY$8}}]),t}(BaseComponent);EventHandler.on(window,EVENT_LOAD_DATA_API$1,(function(){SelectorEngine.find(SELECTOR_DATA_SPY).forEach((function(e){return new ScrollSpy(e,Manipulator.getDataAttributes(e))}))})),onDOMContentLoaded((function(){var e=getjQuery();if(e){var t=e.fn[NAME$8];e.fn[NAME$8]=ScrollSpy.jQueryInterface,e.fn[NAME$8].Constructor=ScrollSpy,e.fn[NAME$8].noConflict=function(){return e.fn[NAME$8]=t,ScrollSpy.jQueryInterface}}}));var NAME$9="tab",DATA_KEY$9="bs.tab",EVENT_KEY$9="."+DATA_KEY$9,DATA_API_KEY$7=".data-api",EVENT_HIDE$3="hide"+EVENT_KEY$9,EVENT_HIDDEN$3="hidden"+EVENT_KEY$9,EVENT_SHOW$3="show"+EVENT_KEY$9,EVENT_SHOWN$3="shown"+EVENT_KEY$9,EVENT_CLICK_DATA_API$6="click"+EVENT_KEY$9+DATA_API_KEY$7,CLASS_NAME_DROPDOWN_MENU="dropdown-menu",CLASS_NAME_ACTIVE$3="active",CLASS_NAME_DISABLED$1="disabled",CLASS_NAME_FADE$3="fade",CLASS_NAME_SHOW$5="show",SELECTOR_DROPDOWN$1=".dropdown",SELECTOR_NAV_LIST_GROUP$1=".nav, .list-group",SELECTOR_ACTIVE$1=".active",SELECTOR_ACTIVE_UL=":scope > li > .active",SELECTOR_DATA_TOGGLE$4='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',SELECTOR_DROPDOWN_TOGGLE$1=".dropdown-toggle",SELECTOR_DROPDOWN_ACTIVE_CHILD=":scope > .dropdown-menu .active",Tab=function(e){function t(){return e.apply(this,arguments)||this}_inheritsLoose(t,e);var n=t.prototype;return n.show=function(){var e=this;if(!(this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&this._element.classList.contains(CLASS_NAME_ACTIVE$3)||this._element.classList.contains(CLASS_NAME_DISABLED$1))){var t,n=getElementFromSelector(this._element),i=this._element.closest(SELECTOR_NAV_LIST_GROUP$1);if(i){var o="UL"===i.nodeName||"OL"===i.nodeName?SELECTOR_ACTIVE_UL:SELECTOR_ACTIVE$1;t=(t=SelectorEngine.find(o,i))[t.length-1]}var r=null;if(t&&(r=EventHandler.trigger(t,EVENT_HIDE$3,{relatedTarget:this._element})),!(EventHandler.trigger(this._element,EVENT_SHOW$3,{relatedTarget:t}).defaultPrevented||null!==r&&r.defaultPrevented)){this._activate(this._element,i);var a=function(){EventHandler.trigger(t,EVENT_HIDDEN$3,{relatedTarget:e._element}),EventHandler.trigger(e._element,EVENT_SHOWN$3,{relatedTarget:t})};n?this._activate(n,n.parentNode,a):a()}}},n._activate=function(e,t,n){var i=this,o=(!t||"UL"!==t.nodeName&&"OL"!==t.nodeName?SelectorEngine.children(t,SELECTOR_ACTIVE$1):SelectorEngine.find(SELECTOR_ACTIVE_UL,t))[0],r=n&&o&&o.classList.contains(CLASS_NAME_FADE$3),a=function(){return i._transitionComplete(e,o,n)};if(o&&r){var s=getTransitionDurationFromElement(o);o.classList.remove(CLASS_NAME_SHOW$5),EventHandler.one(o,TRANSITION_END,a),emulateTransitionEnd(o,s)}else a()},n._transitionComplete=function(e,t,n){if(t){t.classList.remove(CLASS_NAME_ACTIVE$3);var i=SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD,t.parentNode);i&&i.classList.remove(CLASS_NAME_ACTIVE$3),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!1)}(e.classList.add(CLASS_NAME_ACTIVE$3),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!0),reflow(e),e.classList.contains(CLASS_NAME_FADE$3)&&e.classList.add(CLASS_NAME_SHOW$5),e.parentNode&&e.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU))&&(e.closest(SELECTOR_DROPDOWN$1)&&SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE$1).forEach((function(e){return e.classList.add(CLASS_NAME_ACTIVE$3)})),e.setAttribute("aria-expanded",!0));n&&n()},t.jQueryInterface=function(e){return this.each((function(){var n=Data.getData(this,DATA_KEY$9)||new t(this);if("string"==typeof e){if(void 0===n[e])throw new TypeError('No method named "'+e+'"');n[e]()}}))},_createClass(t,null,[{key:"DATA_KEY",get:function(){return DATA_KEY$9}}]),t}(BaseComponent);EventHandler.on(document,EVENT_CLICK_DATA_API$6,SELECTOR_DATA_TOGGLE$4,(function(e){e.preventDefault(),(Data.getData(this,DATA_KEY$9)||new Tab(this)).show()})),onDOMContentLoaded((function(){var e=getjQuery();if(e){var t=e.fn[NAME$9];e.fn[NAME$9]=Tab.jQueryInterface,e.fn[NAME$9].Constructor=Tab,e.fn[NAME$9].noConflict=function(){return e.fn[NAME$9]=t,Tab.jQueryInterface}}}));var NAME$a="toast",DATA_KEY$a="bs.toast",EVENT_KEY$a="."+DATA_KEY$a,EVENT_CLICK_DISMISS$1="click.dismiss"+EVENT_KEY$a,EVENT_HIDE$4="hide"+EVENT_KEY$a,EVENT_HIDDEN$4="hidden"+EVENT_KEY$a,EVENT_SHOW$4="show"+EVENT_KEY$a,EVENT_SHOWN$4="shown"+EVENT_KEY$a,CLASS_NAME_FADE$4="fade",CLASS_NAME_HIDE="hide",CLASS_NAME_SHOW$6="show",CLASS_NAME_SHOWING="showing",DefaultType$7={animation:"boolean",autohide:"boolean",delay:"number"},Default$7={animation:!0,autohide:!0,delay:5e3},SELECTOR_DATA_DISMISS$1='[data-bs-dismiss="toast"]',Toast=function(e){function t(t,n){var i;return(i=e.call(this,t)||this)._config=i._getConfig(n),i._timeout=null,i._setListeners(),i}_inheritsLoose(t,e);var n=t.prototype;return n.show=function(){var e=this;if(!EventHandler.trigger(this._element,EVENT_SHOW$4).defaultPrevented){this._clearTimeout(),this._config.animation&&this._element.classList.add(CLASS_NAME_FADE$4);var t=function(){e._element.classList.remove(CLASS_NAME_SHOWING),e._element.classList.add(CLASS_NAME_SHOW$6),EventHandler.trigger(e._element,EVENT_SHOWN$4),e._config.autohide&&(e._timeout=setTimeout((function(){e.hide()}),e._config.delay))};if(this._element.classList.remove(CLASS_NAME_HIDE),reflow(this._element),this._element.classList.add(CLASS_NAME_SHOWING),this._config.animation){var n=getTransitionDurationFromElement(this._element);EventHandler.one(this._element,TRANSITION_END,t),emulateTransitionEnd(this._element,n)}else t()}},n.hide=function(){var e=this;if(this._element.classList.contains(CLASS_NAME_SHOW$6)&&!EventHandler.trigger(this._element,EVENT_HIDE$4).defaultPrevented){var t=function(){e._element.classList.add(CLASS_NAME_HIDE),EventHandler.trigger(e._element,EVENT_HIDDEN$4)};if(this._element.classList.remove(CLASS_NAME_SHOW$6),this._config.animation){var n=getTransitionDurationFromElement(this._element);EventHandler.one(this._element,TRANSITION_END,t),emulateTransitionEnd(this._element,n)}else t()}},n.dispose=function(){this._clearTimeout(),this._element.classList.contains(CLASS_NAME_SHOW$6)&&this._element.classList.remove(CLASS_NAME_SHOW$6),EventHandler.off(this._element,EVENT_CLICK_DISMISS$1),e.prototype.dispose.call(this),this._config=null},n._getConfig=function(e){return e=_extends({},Default$7,Manipulator.getDataAttributes(this._element),"object"==typeof e&&e?e:{}),typeCheckConfig(NAME$a,e,this.constructor.DefaultType),e},n._setListeners=function(){var e=this;EventHandler.on(this._element,EVENT_CLICK_DISMISS$1,SELECTOR_DATA_DISMISS$1,(function(){return e.hide()}))},n._clearTimeout=function(){clearTimeout(this._timeout),this._timeout=null},t.jQueryInterface=function(e){return this.each((function(){var n=Data.getData(this,DATA_KEY$a);if(n||(n=new t(this,"object"==typeof e&&e)),"string"==typeof e){if(void 0===n[e])throw new TypeError('No method named "'+e+'"');n[e](this)}}))},_createClass(t,null,[{key:"DefaultType",get:function(){return DefaultType$7}},{key:"Default",get:function(){return Default$7}},{key:"DATA_KEY",get:function(){return DATA_KEY$a}}]),t}(BaseComponent);onDOMContentLoaded((function(){var e=getjQuery();if(e){var t=e.fn[NAME$a];e.fn[NAME$a]=Toast.jQueryInterface,e.fn[NAME$a].Constructor=Toast,e.fn[NAME$a].noConflict=function(){return e.fn[NAME$a]=t,Toast.jQueryInterface}}}));export{Alert,Button,Carousel,Collapse,Dropdown,Modal,Popover,ScrollSpy,Tab,Toast,Tooltip};
//# sourceMappingURL=bootstrap.esm.min.js.map